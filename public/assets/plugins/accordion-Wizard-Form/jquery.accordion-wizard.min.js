!function(a,t,e,s){"use strict";var n="accWizard",i={start:1,mode:"wizard",enableScrolling:!0,scrollPadding:5,autoButtons:!0,autoButtonsNextClass:null,autoButtonsPrevClass:null,autoButtonsShowSubmit:!0,autoButtonsSubmitText:"Submit",autoButtonsEditSubmitText:"Save",stepNumbers:!0,stepNumberClass:"",beforeNextStep:function(t){return!0},onSubmit:function(t){return!0}};function c(t,e){this.element=t,this.settings=a.extend({},i,e),this._defaults=i,this._name=n,this.init()}a.extend(c.prototype,{init:function(){var i=this;this.$steps=a("[data-acc-step]"),this.stepHeight=a("[data-acc-step]").eq(0).outerHeight(),this.settings.stepNumbers&&this.$steps.each(function(t,e){a("[data-acc-title]",e).prepend('<span class="acc-step-number '+i.settings.stepNumberClass+'">'+(t+1)+"</span> ")}),this.settings.autoButtons&&this.$steps.each(function(t,e){var s=a("[data-acc-content]",e);if(0<t&&s.append('<a href="#" class="'+i.settings.autoButtonsPrevClass+'" data-acc-btn-prev>Back</a>'),t<i.$steps.length-1)s.append('<a href="#" class="'+i.settings.autoButtonsNextClass+'" data-acc-btn-next>Next</a>');else if(i.settings.autoButtonsShowSubmit){var n="wizard"==i.settings.mode?i.settings.autoButtonsSubmitText:i.settings.autoButtonsEditSubmitText;s.append('<input type="submit" class="'+i.settings.autoButtonsNextClass+'" value="'+n+'">')}}),this.currentIndex=this.settings.start-1,"wizard"==this.settings.mode?(this.activateStep(this.currentIndex,!0),a("[data-acc-btn-next]").on("click",function(){i.settings.beforeNextStep(i.currentIndex+1)&&i.activateStep(i.currentIndex+1)}),a("[data-acc-btn-prev]").on("click",function(){i.activateStep(i.currentIndex-1)})):"edit"==this.settings.mode&&(this.activateAllSteps(),a("[data-acc-btn-next]").hide(),a("[data-acc-btn-prev]").hide()),a(this.element).on("submit",function(t){i.settings.onSubmit()||t.preventDefault()})},deactivateStep:function(t,e){this.$steps.eq(t).removeClass("active")},activateStep:function(t,e){this.$steps.removeClass("open");var s=t>this.currentIndex?this.stepHeight:-this.stepHeight;!e&&this.settings.enableScrolling&&a("html").animate({scrollTop:this.$steps.eq(this.currentIndex).offset().top+(s-this.settings.scrollPadding)},500),a("[data-acc-content]",this.element).slideUp(),this.$steps.eq(t).addClass("open").find("[data-acc-content]").slideDown(),this.currentIndex=t},activateNextStep:function(){this.activateStep(this.currentIndex+1)},activateAllSteps:function(){this.$steps.addClass("open"),a("[data-acc-content]",this.element).show()}}),a.fn[n]=function(t){return this.each(function(){a.data(this,"plugin_"+n)||a.data(this,"plugin_"+n,new c(this,t))})}}(jQuery,window,document);