$((function(){$("#circle").circleProgress({value:.85,size:70,fill:{color:["#d43f8d"]}}),$("#circle-1").circleProgress({value:.64,size:70,fill:{color:["#09ad95"]}}),$("#circle-2").circleProgress({value:.74,size:70,fill:{color:["#f7b731"]}}),$("#circle-3").circleProgress({value:.55,size:70,fill:{gradient:["#f5334f"]}});var o=document.getElementById("areaChart1").getContext("2d");new Chart(o,{type:"line",data:{labels:["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],type:"line",datasets:[{label:"Market value",data:[30,70,30,100,50,130,100,140],backgroundColor:"transparent",borderColor:"#0774f8",pointBackgroundColor:"#fff",pointHoverBackgroundColor:"#0774f8",pointBorderColor:"#0774f8",pointHoverBorderColor:"#0774f8",pointBorderWidth:2,pointRadius:2,pointHoverRadius:2,borderWidth:4}]},options:{maintainAspectRatio:!1,legend:{display:!1},responsive:!0,tooltips:{mode:"index",titleFontSize:12,titleFontColor:"#6b6f80",bodyFontColor:"#6b6f80",backgroundColor:"#fff",titleFontFamily:"Montserrat",bodyFontFamily:"Montserrat",cornerRadius:3,intersect:!1},scales:{xAxes:[{gridLines:{color:"transparent",zeroLineColor:"transparent"},ticks:{fontSize:2,fontColor:"transparent"}}],yAxes:[{display:!1,ticks:{display:!1}}]},title:{display:!1},elements:{line:{borderWidth:1},point:{radius:4,hitRadius:10,hoverRadius:4}}}}),o=document.getElementById("areaChart2").getContext("2d"),new Chart(o,{type:"line",data:{labels:["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],type:"line",datasets:[{label:"Market value",data:[24,18,28,21,32,28,30],backgroundColor:"transparent",borderColor:"#1fc874",pointBackgroundColor:"#fff",pointHoverBackgroundColor:"#1fc874",pointBorderColor:"#1fc874",pointHoverBorderColor:"#1fc874",pointBorderWidth:2,pointRadius:2,pointHoverRadius:2,borderWidth:4}]},options:{maintainAspectRatio:!1,legend:{display:!1},responsive:!0,tooltips:{mode:"index",titleFontSize:12,titleFontColor:"#6b6f80",bodyFontColor:"#6b6f80",backgroundColor:"#fff",titleFontFamily:"Montserrat",bodyFontFamily:"Montserrat",cornerRadius:3,intersect:!1},scales:{xAxes:[{gridLines:{color:"transparent",zeroLineColor:"transparent"},ticks:{fontSize:2,fontColor:"transparent"}}],yAxes:[{display:!1,ticks:{display:!1}}]},title:{display:!1},elements:{line:{borderWidth:1},point:{radius:4,hitRadius:10,hoverRadius:4}}}}),o=document.getElementById("areaChart3").getContext("2d"),new Chart(o,{type:"line",data:{labels:["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],type:"line",datasets:[{label:"Market value",data:[30,70,30,100,50,130,100,140],backgroundColor:"transparent",borderColor:"#f7b731",pointBackgroundColor:"#fff",pointHoverBackgroundColor:"#f7b731",pointBorderColor:"#f7b731",pointHoverBorderColor:"#f7b731",pointBorderWidth:2,pointRadius:2,pointHoverRadius:2,borderWidth:4}]},options:{maintainAspectRatio:!1,legend:{display:!1},responsive:!0,tooltips:{mode:"index",titleFontSize:12,titleFontColor:"#6b6f80",bodyFontColor:"#6b6f80",backgroundColor:"#fff",titleFontFamily:"Montserrat",bodyFontFamily:"Montserrat",cornerRadius:3,intersect:!1},scales:{xAxes:[{gridLines:{color:"transparent",zeroLineColor:"transparent"},ticks:{fontSize:2,fontColor:"transparent"}}],yAxes:[{display:!1,ticks:{display:!1}}]},title:{display:!1},elements:{line:{borderWidth:1},point:{radius:4,hitRadius:10,hoverRadius:4}}}}),o=document.getElementById("areaChart4").getContext("2d"),new Chart(o,{type:"line",data:{labels:["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],type:"line",datasets:[{label:"Market value",data:[24,18,28,21,32,28,30],backgroundColor:"transparent",borderColor:"#f5334f",pointBackgroundColor:"#fff",pointHoverBackgroundColor:"#f5334f",pointBorderColor:"#f5334f",pointHoverBorderColor:"#f5334f",pointBorderWidth:2,pointRadius:2,pointHoverRadius:2,borderWidth:4}]},options:{maintainAspectRatio:!1,legend:{display:!1},responsive:!0,tooltips:{mode:"index",titleFontSize:12,titleFontColor:"#6b6f80",bodyFontColor:"#6b6f80",backgroundColor:"#fff",titleFontFamily:"Montserrat",bodyFontFamily:"Montserrat",cornerRadius:3,intersect:!1},scales:{xAxes:[{gridLines:{color:"transparent",zeroLineColor:"transparent"},ticks:{fontSize:2,fontColor:"transparent"}}],yAxes:[{display:!1,ticks:{display:!1}}]},title:{display:!1},elements:{line:{borderWidth:1},point:{radius:4,hitRadius:10,hoverRadius:4}}}})}));