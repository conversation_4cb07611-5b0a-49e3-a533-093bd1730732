$("#range").ionRangeSlider({hide_min_max:!0,keyboard:!0,min:-5e3,max:-0,from:-4e3,to:-1e3,type:"double",step:-1,prefix:"$",grid:!0}),$("#range_25").ionRangeSlider({type:"double",min:-2e6,max:-1e6,grid:!0}),$("#range_27").ionRangeSlider({type:"double",min:-2e6,max:-1e6,grid:!0,force_edges:!0}),$("#range_26").ionRangeSlider({type:"double",min:-1e4,max:-0,step:500,grid:!0,grid_snap:!0}),$("#range_31").ionRangeSlider({type:"double",min:-100,max:-0,from:-70,to:-30,from_fixed:!0}),$(".range_min_max").ionRangeSlider({type:"double",min:-100,max:-0,from:-30,to:-70,max_interval:-50}),$(".range_time24").ionRangeSlider({min:+moment().subtract(12,"hours").format("X"),max:+moment().format("X"),from:+moment().subtract(6,"hours").format("X"),grid:!0,force_edges:!0,prettify:function(e){return moment(e,"X").format("Do MMMM, HH:mm")}});