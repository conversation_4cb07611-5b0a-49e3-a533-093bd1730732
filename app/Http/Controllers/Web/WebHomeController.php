<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Mail\ContactMail;
use App\Mail\ContactConfirmationMail;
use App\Rules\RecaptchaRule;
use App\Models\Banner;
use App\Models\Blog;
use App\Models\Category;
use App\Models\Certificate;
use App\Models\Impact;
use App\Models\Material;
use App\Models\Product;
use App\Models\Story;
use App\Models\Team;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;

class WebHomeController extends Controller
{
    public function index()
    {
        $categories = Category::whereNullOrEmptyOrZero('parent_id')->orderBy('id', 'desc')->get();

        $blogs = Blog::orderBy('id', 'desc')->limit(3)->get();

        return view('web.index', compact('categories', 'blogs'));
    }

    public function aboutPage()
    {
        return view('web.about.about');
    }

    public function teamListPage()
    {
        $teams = Team::get();

        return view('web.about.team.team', compact('teams'));
    }

    public function teamPage(Team $team)
    {
        return view('web.about.team.open-team', compact('team'));
    }

    public function indexStory()
    {
        $story = Story::get();

        return view('web.story.story', compact('story'));
    }

    public function indexService()
    {
        return view('web.services.services');
    }

    public function indexContact()
    {
        return view('web.contact.contact');
    }

    public function activeties()
    {
        $gallarys = Material::whereNullOrEmptyOrZero('parent_id')->get();

        return view('web.multimedia.gallary', compact('gallarys'));
    }

    public function whatWeDo()
    {
        $categories = Category::whereNullOrEmptyOrZero('parent_id')->get();

        return view('web.row-material.row-material', compact('categories'));
    }

    public function singleRowMaterial(Category $singleCategory)
    {
        return view('web.row-material.view-raw-single-page', compact('singleCategory'));
    }

    public function sustainabiltiy()
    {
        $sustainabiltiy = Impact::orderBy('id', 'desc')->get();
        return view('web.sustainabilty.sustainabilty', compact('sustainabiltiy'));
    }

    public function sustainabiltiyInside(Impact $sustainabiltiy)
    {
        $sustainabilties = Impact::orderBy('id', 'desc')->get();
        return  view('web.sustainabilty.view-sus', compact('sustainabiltiy', 'sustainabilties'));
    }

    public function production()
    {
        $certificates = Certificate::get();
        return view('web.production.production-page', compact('certificates'));
    }

    public function send_email(Request $request)
    {
        // Prepare validation rules
        $rules = [
            'name' => 'required|string|max:255',
            'email' => 'required|email:rfc,dns|max:255',
            'comment' => 'required|string|max:2000',
        ];

        $messages = [
            'name.required' => 'Please enter your name.',
            'email.required' => 'Please enter your email address.',
            'email.email' => 'Please enter a valid email address.',
            'comment.required' => 'Please enter your message.',
        ];

        // Add reCAPTCHA validation only if it's configured
        if (config('services.recaptcha.secret_key')) {
            $rules['g-recaptcha-response'] = ['required', new RecaptchaRule()];
            $messages['g-recaptcha-response.required'] = 'Please complete the reCAPTCHA verification.';
        }

        // Validate the form data
        $validatedData = $request->validate($rules, $messages);

        try {
            // Clean and validate the email address
            $userEmail = trim(strtolower($validatedData['email']));

            // Additional email validation to prevent encoding issues
            if (!filter_var($userEmail, FILTER_VALIDATE_EMAIL)) {
                throw new \Exception('Invalid email format: ' . $userEmail);
            }

            // Log the email for debugging
            \Log::info('Contact form submission - User email: ' . $userEmail);

            $mails = [
                'name' => $validatedData['name'],
                'email' => $userEmail,
                'comment' => $validatedData['comment'],
            ];

            // Send notification email to admin
            Mail::to(config('mail.from.address'))->send(new ContactMail($mails));
            \Log::info('Admin notification email sent successfully');

            // Send confirmation email to user
            Mail::to($userEmail)->send(new ContactConfirmationMail($mails));
            \Log::info('User confirmation email sent successfully to: ' . $userEmail);

            return redirect()->back()->with('success', 'Thank you for your message! We will get back to you soon.');
        } catch (\Exception $e) {
            \Log::error('Contact form submission failed: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Sorry, there was an error sending your message. Please try again later.');
        }
    }
}
