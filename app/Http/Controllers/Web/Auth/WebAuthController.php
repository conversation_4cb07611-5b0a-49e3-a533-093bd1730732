<?php

namespace App\Http\Controllers\Web\Auth;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class WebAuthController extends Controller
{
    public function loginPage()
    {
        return view('web.auth.login');
    }

    public function login(Request $request)
    {
        $logins = $request->validate([
            'email_l'     => 'required|email|max:100',
            'password_l' => 'required',
        ], [
            'email_l.required' => 'The email field is required',
            'password_l.required' => 'The password field is required',
        ]);

        $cred = [
            'email' => $request->email_l,
            'password' => $request->password_l,
        ];

        if (Auth::attempt($cred)) {

            $request->session()->regenerate();

            auth()->user()->linkOrderAfterGuest();

            $user = auth()->user();

            if ($user->hasRole('owner') || $user->hasRole('employee') || $user->hasRole('admin')) {
                return redirect()->route('dashboard')->withSuccess(__('global.You-have-successfully-registered-logged-in'));
            }

            return redirect()->route('web.index')->withSuccess(__('global.You-have-successfully-registered-logged-in'));
        }

        return back()->withErrors([
            'email' => __('global.Your-provided-credentials-do-not-match-in-our-records'),
        ])->onlyInput('email');
    }

    public function signUpPage()
    {
        return view('web.auth.login');
    }

    public function signup(Request $request)
    {
        $valedate = $request->validate(
            [
                'first_name'    => 'required|string|max:100',
                'last_name'    => 'required|string|max:100',
                'email_s'     => 'required|email|max:100|unique:users,email',
                'phone'      => 'required|string|max:100',
                'password'  => 'required|min:8'
            ]
        );

        if (!$valedate)
            return back()->withErrors([
                'email_s' => __('global.Your-provided-credentials-do-not-match-in-our-records'),
            ])->onlyInput(['f_name', 'l_name', 'email_s']);


        $user = User::create([
            'first_name' => $request->first_name,
            'last_name'  => $request->last_name,
            'email'      => $request->email_s,
            'password'   => Hash::make($request->password),
            'phone'     => $request->phone

        ]);

        // $credentials = $request->only('email_s', 'password');

        $cred = [
            'email' => $request->email_l,
            'password' => $request->password_l,
        ];

        Auth::attempt($cred);

        $request->session()->regenerate();

        $user->linkOrderAfterGuest();

        return redirect()->route('web.index')->withSuccess(__('global.You-have-successfully-registered-logged-in'));
    }

    public function logout(Request $request)
    {
        Auth::logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        return redirect()->route('web.login_page')->withSuccess(__('global.You-have-logged-out-successfully'));
    }

    public function dashboard()
    {
        return view('web.account.account-dashboard');
    }

    public function accountOrder(Request $request)
    {

        return view('web.account.account-order');
    }

    // public function deleteOrder() {}
    public function accountDetails()
    {
        return view('web.account.account-details');
    }

    public function accountUpdate(Request $request)
    {
        $user = User::find($request->user()->id);

        $user->update([
            'first_name' => $request->first_name,
            'last_name' => $request->last_name,
            'email' => $request->email
        ]);

        return redirect()->back()->withSuccess(__('global.Your-data-updated-successfully'));
    }

    public function viewOrder(Order $order)
    {
        return view('web.order.order-page', compact('order'));
    }
}
