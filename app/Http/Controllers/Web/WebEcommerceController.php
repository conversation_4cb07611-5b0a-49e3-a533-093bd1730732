<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\Address;
use App\Models\Category;
use App\Models\Item;
use App\Models\Order;
use App\Models\Product;
use App\Models\Rate;
use Illuminate\Http\Request;

class WebEcommerceController extends Controller
{

    public function categories()
    {
        $categories = Category::whereNullOrEmptyOrZero('parent_id')->get();

        return view('web.categories.categories', compact('categories'));
    }
    public function index()
    {
        return view('web.product.product');
    }

    public function productPage(Product $product)
    {
        $rates = Rate::where('product_id', $product->id)->get();

        return view('web.product.product-page', compact('product', 'rates'));
    }

    public function addRate(Request $request)
    {
        $user_id = auth()->user()->id;

        Rate::create([
            'product_id' => $request->product_id,
            'user_id' => $user_id,
            'value' => $request->input('rating'),
            'text' => $request->input('comment'),
        ]);

        return redirect()->back()->with('message', 'your comment add');
    }

    // 

    public function cartPage()
    {
        return view('web.cart.cart-page');
    }

    public function addToCart(Request $request)
    {
        $user = auth()->user();
        $guest_id = session()->get('guest_id', 0);

        $product = Product::find($request->product_id);

        if (!$product)
            return response()->json([
                'error' => __('global.product-not-found')
            ]);

        $order = null;
        if ($user) {
            $order = Order::where('user_id', $user->id)
                ->where('order_status_id', 1)->latest()->first();
        } else {
            $order = Order::where('add_by', $guest_id)
                ->where('user_id', 0)
                ->where('order_status_id', 1)->latest()->first();
        }


        if (!$order) {

            $address = Address::create([
                'user_id' => $user ? 0 : $guest_id,
            ]);

            $order = Order::create([
                'add_by' => $user ? 0 : $guest_id,
                'slug' => rand(100000, 999999),
                'user_id' => $user ? $user->id : 0,
                'order_status_id' => 1,
                'address_id' => $address->id,
            ]);
        }

        $item = Item::where('order_id', $order->id)->where('product_id', $product->id)->first();


        if ($item) {
            $item->quantity += $request->quantity;
            $item->save();
        } else {
            // attribute_value_id
            Item::create([
                'add_by' => $user ? 0 : $guest_id,
                'order_id' => $order->id,
                'product_id' => $product->id,
                'price' => $product->attribute_value->final_price(),
                'quantity' => $request->quantity,
                'note' => $request->note,
            ]);
        }

        $order->refresh();
        $order->calPrice();

        return response()->json([
            'success' => __('global.product-added-successfully'),
            'order' => $order,
        ]);
    }

    public function checkoutPage()
    {
        return view('web.cart.checkout-page');
    }
}
