<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class AttributeValueResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        // return parent::toArray($request);
        return [
            "attributevalue_id" => $this->id,
            "slug" => $this->slug,

            
                        'sku' => $this->sku,
                        'the_name' => $this->the_name,
                        'the_description' => $this->the_description,
                        'price' => $this->price,
                        'discount_type' => $this->discount_type,
                        'discount_amount' => $this->discount_amount,
                        'manage_stock' => $this->manage_stock,
                        'quantity_in_stock' => $this->quantity_in_stock,
                        'discount_from_date' => $this->discount_from_date,
                        'discount_to_date' => $this->discount_to_date,
                        'status' => $this->status,
                    'attribute_group' => new Attribute_groupResource($this->whenLoaded('attribute_group')),
                    'product' => new ProductResource($this->whenLoaded('product')),

            "date" => date("d/m/Y", strtotime($this->created_at)),
            "time" => date("h:i A", strtotime($this->created_at)),
            "date_time" => date("Y-m-d h:i A", strtotime($this->created_at)),
            "created_ago" => $this->created_ago($this->created_at, $request->header("lang")),
        ];
    }
}
