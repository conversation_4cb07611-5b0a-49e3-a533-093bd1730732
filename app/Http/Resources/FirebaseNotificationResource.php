<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class FirebaseNotificationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        // return parent::toArray($request);
        return [
            "firebasenotification_id" => $this->id,
            "slug" => $this->slug,

            
                        'user_id' => $this->user_id,
                        'notification_text_id' => $this->notification_text_id,
                        'image' => $this->image != null ? asset($this->image) : null,
                        'the_page' => $this->the_page,
                        'title' => $this->title,
                        'body' => $this->body,
                        'data_id' => $this->data_id,
                        'seen' => $this->seen,
                        'seen_at' => $this->seen_at,

            "date" => date("d/m/Y", strtotime($this->created_at)),
            "time" => date("h:i A", strtotime($this->created_at)),
            "date_time" => date("Y-m-d h:i A", strtotime($this->created_at)),
            "created_ago" => $this->created_ago($this->created_at, $request->header("lang")),
        ];
    }
}
