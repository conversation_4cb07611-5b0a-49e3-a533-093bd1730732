<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class TeamResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        // return parent::toArray($request);
        return [
            "team_id" => $this->id,
            "slug" => $this->slug,

            
                        'the_name' => $this->the_name,
                        'the_sub_title' => $this->the_sub_title,
                        'the_bio' => $this->the_bio,
                        'image' => $this->image,
                        'phone' => $this->phone,
                        'email' => $this->email,
                        'fb_link' => $this->fb_link,
                        'tw_link' => $this->tw_link,
                        'in_link' => $this->in_link,
                        'ln_link' => $this->ln_link,

            "date" => date("d/m/Y", strtotime($this->created_at)),
            "time" => date("h:i A", strtotime($this->created_at)),
            "date_time" => date("Y-m-d h:i A", strtotime($this->created_at)),
            "created_ago" => $this->created_ago($this->created_at, $request->header("lang")),
        ];
    }
}
