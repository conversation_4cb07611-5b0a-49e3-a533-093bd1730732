<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class ImpactResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        // return parent::toArray($request);
        return [
            "impact_id" => $this->id,
            "slug" => $this->slug,

            
                        'the_title' => $this->the_title,
                        'the_desc' => $this->the_desc,
                        'the_sub_desc' => $this->the_sub_desc,
                        'link' => $this->link,
                        'image' => $this->image,
                        'layout' => $this->layout,

            "date" => date("d/m/Y", strtotime($this->created_at)),
            "time" => date("h:i A", strtotime($this->created_at)),
            "date_time" => date("Y-m-d h:i A", strtotime($this->created_at)),
            "created_ago" => $this->created_ago($this->created_at, $request->header("lang")),
        ];
    }
}
