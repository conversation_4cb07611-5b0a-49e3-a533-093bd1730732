<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class OrderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        // return parent::toArray($request);
        return [
            "order_id" => $this->id,
            "slug" => $this->slug,

            
                    'user' => new UserResource($this->whenLoaded('user')),
                    'order_status' => new Order_statusResource($this->whenLoaded('order_status')),
                        'total_price' => $this->total_price,
                        'delivery_date' => $this->delivery_date,
                        'delivery_status' => $this->delivery_status,
                        'delivery_fee' => $this->delivery_fee,
                        'payment_method' => $this->payment_method,
                        'payment_status' => $this->payment_status,
                    'address' => new AddressResource($this->whenLoaded('address')),
                    'promo_code' => new Promo_codeResource($this->whenLoaded('promo_code')),
                        'discount_type' => $this->discount_type,
                        'discount_amount' => $this->discount_amount,
                        'final_price' => $this->final_price,

            "date" => date("d/m/Y", strtotime($this->created_at)),
            "time" => date("h:i A", strtotime($this->created_at)),
            "date_time" => date("Y-m-d h:i A", strtotime($this->created_at)),
            "created_ago" => $this->created_ago($this->created_at, $request->header("lang")),
        ];
    }
}
