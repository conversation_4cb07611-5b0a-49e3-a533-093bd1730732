<?php

namespace App\Http\Livewire\Contact;

use App\Models\Contact;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Livewire\Component;
use Livewire\WithPagination;

class ContactIndex extends Component
{
    use WithPagination;

    protected $paginationTheme = 'bootstrap';

    public $perPage = 25;
    public $search = '';
    public $orderBy = 'id';
    public $orderWay = 'desc';
    public $showColumn;

    public $all;
    public $fromDate = null;
    public $toDate = null;
    public $byDate = 'created_at';

    public $selectedContacts = [];

    public Contact $contact;
    private $contacts;
    public $user;





    public $url;
    public $admin_view_status = '';
    public function mount($admin_view_status = '')
    {
        $this->url = Route::current()->getName();
        $this->admin_view_status = $admin_view_status;
        $this->user = Auth::user();

        $this->contact = new Contact();

        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->toDate = date('Y-m-d');




        $this->showColumn = collect([
            'id' => false,
            'slug' => false,


            'name' => true,
            'email' => true,
            'phone' => true,
            'title' => true,
            'message' => true,

            // 'status' => false,
            'date' => true,
            'time' => true,
        ]);
    }

    public $slug;
    public $contact_id, $name, $email, $phone, $title, $message;
    public $updateMode = false;

    private function resetInputFields()
    {
        $this->slug = '';

        $this->name = '';
        $this->email = '';
        $this->phone = '';
        $this->title = '';
        $this->message = '';
    }


    public function rules()
    {
        return [
            // 'patient_id' => "required|unique:patient_studies,patient_id,$this->patientstudy_id,id,study_id,$this->study_id,deleted_at,NULL",
            // 'slug' => $this-slug,


            'name' => 'required',
            'email' => 'required',
            'phone' => 'required',
            'title' => 'required',
            'message' => 'required',
        ];
    }

    public function updated($propertyName)
    {
        $this->validateOnly($propertyName);
    }

    public function store()
    {
        $validatedData = $this->validate();

        Contact::create([
            'slug' => $this->slug,

            'name' => $this->name,
            'email' => $this->email,
            'phone' => $this->phone,
            'title' => $this->title,
            'message' => $this->message,
        ]);

        session()->flash('message', 'Contact Created Successfully.');

        $this->resetInputFields();

        // $this->emit('close-model'); // Close model to using to jquery
    }

    public function edit($id)
    {
        $this->updateMode = true;
        $contact = Contact::where('id', $id)->first();
        $this->contact_id = $id;
        $this->slug = $contact->slug;


        $this->name = $contact->name;
        $this->email = $contact->email;
        $this->phone = $contact->phone;
        $this->title = $contact->title;
        $this->message = $contact->message;
    }

    public function cancel()
    {
        $this->updateMode = false;
        $this->resetInputFields();
    }

    public function update()
    {

        if ($this->contact_id) {
            $contact = Contact::find($this->contact_id);
            $contact->update([
                'slug' => $this->slug,

                'name' => $this->name,
                'email' => $this->email,
                'phone' => $this->phone,
                'title' => $this->title,
                'message' => $this->message,
            ]);

            $this->updateMode = false;
            session()->flash('message', 'Contact Updated Successfully.');
            $this->resetInputFields();
        }

        $this->emit('close-model'); // Close model to using to jquery
    }

    public function delete($id)
    {
        if ($id) {
            $contact = Contact::find($id);

            $contact->delete();

            session()->flash('message', 'Contact Deleted Successfully.');
        }
    }

    public function restore($id)
    {
        if ($id) {
            $contact = Contact::withTrashed()->find($id);

            $contact->restore();

            session()->flash('message', 'Contact Recovered Successfully.');
        }
    }

    public function updatedFromDate($fromDate)
    {
        $this->all = false;
    }

    public function updatedToDate($toDate)
    {
        $this->all = false;
    }

    public function clearFilter()
    {
        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->byDate = 'created_at';
        $this->fromDate = '';
        $this->toDate = date('Y-m-d');
    }




    public function gotoPage($page)
    {
        $this->setPage($page);
        $this->emit('gotoTop');
    }

    public function nextPage()
    {
        $this->setPage($this->page + 1);
        $this->emit('gotoTop');
    }

    public function previousPage()
    {
        $this->setPage(max($this->page - 1, 1));
        $this->emit('gotoTop');
    }

    public function render()
    {
        $contacts = Contact::livewireSearch($this->search);

        if ($this->all == false)
            $contacts = $contacts->whereBetween($this->byDate, [$this->fromDate . ' 00:00:00', $this->toDate . ' 23:59:59']);




        if ($this->admin_view_status == 'deleted')
            $contacts = $contacts->onlyTrashed();


        $contacts = $contacts->orderBy($this->orderBy, $this->orderWay)
            ->paginate($this->perPage);

        return view('livewire.contact.contact-index', compact('contacts'));
    }
}
