<?php

namespace App\Http\Livewire\Banner;

use App\Http\Controllers\HomeController;
use App\Models\Banner;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Livewire\Component;
use Livewire\WithFileUploads;
use Livewire\WithPagination;

class BannerIndex extends Component
{
    use WithPagination;
    use WithFileUploads;


    protected $paginationTheme = 'bootstrap';

    public $perPage = 25;
    public $search = '';
    public $orderBy = 'id';
    public $orderWay = 'desc';
    public $showColumn;

    public $all;
    public $fromDate = null;
    public $toDate = null;
    public $byDate = 'created_at';

    public $selectedBanners = [];

    public Banner $banner;
    private $banners;
    public $user;





    public $url;
    public $admin_view_status = '';
    public function mount($admin_view_status = '')
    {
        $this->url = Route::current()->getName();
        $this->admin_view_status = $admin_view_status;
        $this->user = Auth::user();

        $this->banner = new Banner();

        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->toDate = date('Y-m-d');




        $this->showColumn = collect([
            'id' => false,
            'slug' => false,


            'image' => true,
            'the_title' => false,
            'the_description' => false,
            'the_model' => false,
            'the_model_id' => false,
            'place' => false,
            'status' => true,
            'from_date' => true,
            'to_date' => true,

            // 'status' => false,
            'date' => true,
            'time' => false,
        ]);
    }

    public $slug;
    public $banner_id, $image, $the_title, $the_description, $the_model, $the_model_id, $place, $status, $from_date, $to_date;
    public $updateMode = false;

    private function resetInputFields()
    {
        $this->slug = '';

        $this->image = '';
        $this->the_title = '';
        $this->the_description = '';
        $this->the_model = '';
        $this->the_model_id = null;
        $this->place = '';
        $this->status = '';
        $this->from_date = '';
        $this->to_date = '';
    }


    public function rules()
    {
        return [
            // 'patient_id' => "required|unique:patient_studies,patient_id,$this->patientstudy_id,id,study_id,$this->study_id,deleted_at,NULL",
            // 'slug' => $this-slug,


            'image' => 'required',
            'the_title' => 'required',
            'the_description' => 'required',
            // 'the_model' => 'required',
            // 'the_model_id' => 'required',
            'place' => 'required',
            'status' => 'required',
            'from_date' => 'required',
            'to_date' => 'required',
        ];
    }

    public function updated($propertyName)
    {
        $this->validateOnly($propertyName);
    }

    public function store()
    {
        $validatedData = $this->validate();



        Banner::create([
            // 'slug' => $this->slug,

            'image' => HomeController::saveImageWeb($this->image, 'banner'),
            'the_title' => $this->the_title,
            'the_description' => $this->the_description,
            'the_model' => $this->the_model,
            'the_model_id' => $this->the_model_id,
            'place' => $this->place,
            'status' => $this->status,
            'from_date' => $this->from_date,
            'to_date' => $this->to_date,
        ]);

        session()->flash('message', 'Banner Created Successfully.');

        $this->resetInputFields();

        // $this->emit('close-model'); // Close model to using to jquery
    }

    public function edit($id)
    {
        $this->updateMode = true;
        $banner = Banner::where('id', $id)->first();
        $this->banner_id = $id;
        $this->slug = $banner->slug;


        // $this->image = $banner->image;
        $this->the_title = $banner->the_title;
        $this->the_description = $banner->the_description;
        $this->the_model = $banner->the_model;
        $this->the_model_id = $banner->the_model_id;
        $this->place = $banner->place;
        $this->status = $banner->status;
        $this->from_date = $banner->from_date;
        $this->to_date = $banner->to_date;
    }

    public function cancel()
    {
        $this->updateMode = false;
        $this->resetInputFields();
    }

    public function update()
    {

        if ($this->banner_id) {
            $banner = Banner::find($this->banner_id);
            $banner->update([
                'slug' => $this->slug,

                'image' => $this->image ? HomeController::saveImageWeb($this->image, 'banner') : $banner->image,
                'the_title' => $this->the_title,
                'the_description' => $this->the_description,
                'the_model' => $this->the_model,
                'the_model_id' => $this->the_model_id,
                'place' => $this->place,
                'status' => $this->status,
                'from_date' => $this->from_date,
                'to_date' => $this->to_date,
            ]);

            $this->updateMode = false;
            session()->flash('message', 'Banner Updated Successfully.');
            $this->resetInputFields();
        }

        $this->emit('close-model'); // Close model to using to jquery
    }

    public function delete($id)
    {
        if ($id) {
            $banner = Banner::find($id);

            $banner->delete();

            session()->flash('message', 'Banner Deleted Successfully.');
        }
    }

    public function restore($id)
    {
        if ($id) {
            $banner = Banner::withTrashed()->find($id);

            $banner->restore();

            session()->flash('message', 'Banner Recovered Successfully.');
        }
    }

    public function updatedFromDate($fromDate)
    {
        $this->all = false;
    }

    public function updatedToDate($toDate)
    {
        $this->all = false;
    }

    public function clearFilter()
    {
        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->byDate = 'created_at';
        $this->fromDate = '';
        $this->toDate = date('Y-m-d');
    }




    public function gotoPage($page)
    {
        $this->setPage($page);
        $this->emit('gotoTop');
    }

    public function nextPage()
    {
        $this->setPage($this->page + 1);
        $this->emit('gotoTop');
    }

    public function previousPage()
    {
        $this->setPage(max($this->page - 1, 1));
        $this->emit('gotoTop');
    }

    public function render()
    {
        $banners = Banner::livewireSearch($this->search);

        if ($this->all == false)
            $banners = $banners->whereBetween($this->byDate, [$this->fromDate . ' 00:00:00', $this->toDate . ' 23:59:59']);




        if ($this->admin_view_status == 'deleted')
            $banners = $banners->onlyTrashed();


        $banners = $banners->orderBy($this->orderBy, $this->orderWay)
            ->paginate($this->perPage);

        return view('livewire.banner.banner-index', compact('banners'));
    }
}
