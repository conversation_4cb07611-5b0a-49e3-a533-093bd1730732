<?php

namespace App\Http\Livewire\Area;

use App\Models\Area;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Livewire\Component;
use Livewire\WithPagination;

class AreaIndex extends Component
{
    use WithPagination;

    protected $paginationTheme = 'bootstrap';

    public $perPage = 25;
    public $search = '';
    public $orderBy = 'id';
    public $orderWay = 'desc';
    public $showColumn;

    public $all;
    public $fromDate = null;
    public $toDate = null;
    public $byDate = 'created_at';

    public $selectedAreas = [];

    public Area $area;
    private $areas;
    public $user;


    public $cities = [];



    public $filter_cities_id = [];


    public $url;
    public $admin_view_status = '';
    public function mount($admin_view_status = '')
    {
        $this->url = Route::current()->getName();
        $this->admin_view_status = $admin_view_status;
        $this->user = Auth::user();

        $this->area = new Area();

        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->toDate = date('Y-m-d');



        $this->cities = \App\Models\City::where('show', 1)->orderBy('sort')->get();


        $this->showColumn = collect([
            'id' => false,
            'slug' => false,


            'the_name' => true,
            'delivery_fee' => true,
            'status' => true,
            'city_id' => true,

            // 'status' => false,
            'date' => true,
            'time' => false,
        ]);
    }

    public $slug;
    public $area_id, $the_name, $delivery_fee, $status, $city_id;
    public $updateMode = false;

    private function resetInputFields()
    {
        $this->slug = '';

        $this->the_name = '';
        $this->delivery_fee = '';
        $this->status = '';
        $this->city_id = null;
    }


    public function rules()
    {
        return [
            // 'patient_id' => "required|unique:patient_studies,patient_id,$this->patientstudy_id,id,study_id,$this->study_id,deleted_at,NULL",
            // 'slug' => $this-slug,


            'the_name' => 'required',
            'delivery_fee' => 'required',
            'status' => 'required',
            'city_id' => 'required',
        ];
    }

    public function updated($propertyName)
    {
        $this->validateOnly($propertyName);
    }

    public function store()
    {
        $validatedData = $this->validate();

        Area::create([
            'slug' => $this->slug,

            'the_name' => $this->the_name,
            'delivery_fee' => $this->delivery_fee,
            'status' => $this->status,
            'city_id' => $this->city_id,
        ]);

        session()->flash('message', 'Area Created Successfully.');

        $this->resetInputFields();

        // $this->emit('close-model'); // Close model to using to jquery
    }

    public function edit($id)
    {
        $this->updateMode = true;
        $area = Area::where('id', $id)->first();
        $this->area_id = $id;
        $this->slug = $area->slug;


        $this->the_name = $area->the_name;
        $this->delivery_fee = $area->delivery_fee;
        $this->status = $area->status;
        $this->city_id = $area->city_id;
    }

    public function cancel()
    {
        $this->updateMode = false;
        $this->resetInputFields();
    }

    public function update()
    {

        if ($this->area_id) {
            $area = Area::find($this->area_id);
            $area->update([
                'slug' => $this->slug,

                'the_name' => $this->the_name,
                'delivery_fee' => $this->delivery_fee,
                'status' => $this->status,
                'city_id' => $this->city_id,
            ]);

            $this->updateMode = false;
            session()->flash('message', 'Area Updated Successfully.');
            $this->resetInputFields();
        }

        $this->emit('close-model'); // Close model to using to jquery
    }

    public function delete($id)
    {
        if ($id) {
            $area = Area::find($id);

            $area->delete();

            session()->flash('message', 'Area Deleted Successfully.');
        }
    }

    public function restore($id)
    {
        if ($id) {
            $area = Area::withTrashed()->find($id);

            $area->restore();

            session()->flash('message', 'Area Recovered Successfully.');
        }
    }

    public function updatedFromDate($fromDate)
    {
        $this->all = false;
    }

    public function updatedToDate($toDate)
    {
        $this->all = false;
    }

    public function clearFilter()
    {
        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->byDate = 'created_at';
        $this->fromDate = '';
        $this->toDate = date('Y-m-d');


        $this->filter_cities_id = [];
    }


    public $select_city;
    public function updatedSelectCity($val)
    {
        $this->filter_cities_id[] = $val;
    }




    public function gotoPage($page)
    {
        $this->setPage($page);
        $this->emit('gotoTop');
    }

    public function nextPage()
    {
        $this->setPage($this->page + 1);
        $this->emit('gotoTop');
    }

    public function previousPage()
    {
        $this->setPage(max($this->page - 1, 1));
        $this->emit('gotoTop');
    }

    public function render()
    {
        $areas = Area::livewireSearch($this->search);

        if ($this->all == false)
            $areas = $areas->whereBetween($this->byDate, [$this->fromDate . ' 00:00:00', $this->toDate . ' 23:59:59']);



        if ($this->filter_cities_id)
            $areas = $areas->whereIn('city_id', $this->filter_cities_id);


        if ($this->admin_view_status == 'deleted')
            $areas = $areas->onlyTrashed();


        $areas = $areas->orderBy($this->orderBy, $this->orderWay)
            ->paginate($this->perPage);

        return view('livewire.area.area-index', compact('areas'));
    }
}
