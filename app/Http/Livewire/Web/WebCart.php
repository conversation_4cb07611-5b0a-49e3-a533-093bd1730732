<?php

namespace App\Http\Livewire\Web;

use App\Models\Area;
use App\Models\City;
use App\Models\Country;
use App\Models\Item;
use App\Models\Order;
use App\Models\PromoCode;
use Livewire\Component;

class WebCart extends Component
{
    public $guest_id;
    public $order;

    public $code = '';
    public $promoCode;

    public function mount()
    {
        $this->guest_id = session()->get('guest_id', 0);
    }

    public function addQuantity($item_id)
    {
        $item = Item::find($item_id);

        $item->increment('quantity');

        $this->order->refresh();
        $this->order->calPrice();

        $this->emit('increment-quantity');
    }

    public function removeQuantity($item_id)
    {
        $item = Item::find($item_id);

        if (!$item) return;

        if ($item->quantity == 1) {
            $item->delete();
            $this->emit('item-removed');
        } else {
            $item->decrement('quantity');
            $this->emit('decrement-quantity');
        }

        $this->order->refresh();
        $this->order->calPrice();
    }

    public function removeItem($item_id)
    {

        $item = Item::find($item_id);

        if (!$item) return;

        $item->delete();

        $this->order->refresh();
        $this->order->calPrice();

        $this->emit('item-removed');
    }

    public function clearPromoCode()
    {
        $this->code = '';
        $this->promoCode = null;

        $this->order->update([
            'promo_code_id' => 0,
            'discount_type' => '',
            'discount_amount' => 0,
        ]);

        $this->order->refresh();
        $this->order->calPrice();
    }
    public function checkPromoCode()
    {
        if ($this->code == '')
            return back()->with([
                'error' => __('global.pleae-enter-code'),
            ]);;


        $promoCode = PromoCode::where('code', $this->code)->latest()->first();

        if (!$promoCode)
            return back()->with([
                'error' => __('global.the-promo-code-you-used-not-found'),
            ]);

        if (!$promoCode->active())
            return back()->with([
                'error' => __('global.the-promo-code-not-active'),
            ]);


        $subPrice = 0;

        foreach ($this->order->items as $item)
            $subPrice += $item->final_price();

        $this->order->update([
            'promo_code_id' => $promoCode->id,
            'discount_type' => $promoCode->discount_type,
            'discount_amount' => $promoCode->theDiscount($subPrice),
        ]);

        $this->order->refresh();
        $this->order->calPrice();

        $this->promoCode = $promoCode;

        return back()->with([
            'success' => __('global.promo-code-used'),
        ]);
    }



    public function render()
    {
        if (auth()->user())
            $this->order = Order::where('user_id', auth()->user()->id)->where('order_status_id', 1)->latest()->first();
        else
            $this->order = Order::where('add_by', $this->guest_id)->where('order_status_id', 1)->latest()->first();

        if ($this->order && $this->order->promo_code) {
            $this->promoCode = $this->order->promo_code;
            $this->code = $this->order->promo_code->code;
        }

        return view('livewire.web.web-cart');
    }
}
