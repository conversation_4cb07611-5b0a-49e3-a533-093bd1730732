<?php

namespace App\Http\Livewire\Web;

use App\Models\Contact;
use App\Models\Item;
use App\Models\Order;
use App\Models\Product;
use Livewire\Component;

class WebProduct extends Component
{
    public $product;

    public $order;

    public $guest_id;

    public $orderEmail;
    public $name;
    public $email;
    public $phone;

    public $message;

    public function mount($product)
    {
        $this->product = $product;

        $this->name = $product->name();
    }

    public function addProductToCart()
    {
        $this->guest_id = session()->get('guest_id', 0);

        if (auth()->user())
            $this->order = Order::where('user_id', auth()->user()->id)->where('order_status_id', 1)->latest()->first();
        else
            $this->order = Order::where('add_by', $this->guest_id)->where('order_status_id', 1)->latest()->first();
    }

    public function addQuantity($item_id)
    {
        $item = Item::find($item_id);

        $item->increment('quantity');

        $this->order->refresh();
        $this->order->calPrice();

        $this->emit('increment-quantity');
    }

    public function removeQuantity($item_id)
    {
        $item = Item::find($item_id);

        if (!$item) return;

        if ($item->quantity == 1) {
            $item->delete();
            $this->emit('item-removed');
        } else {
            $item->decrement('quantity');
            $this->emit('decrement-quantity');
        }

        $this->order->refresh();
        $this->order->calPrice();
    }

    public function requestOrder()
    {
        Contact::create([
            'name' => auth()->user()->crud_name(),
            'email' => $this->email,
            'phone' => $this->phone,
            'title' => 'request quantity',
            'message' => $this->message,
        ]);

       
    }

    public function render()
    {
        return view('livewire.web.web-product');
    }
}
