<?php

namespace App\Http\Livewire\Web;

use App\Models\Product;
use Livewire\Component;

class WebSearchSection extends Component
{
    public $query, $products;

    public function searchQuery()
    {
        // $this->products = Product::whereRaw("lower(`the_name`) like '%" . strtolower($this->query) . "%'")->get();
        $this->products = Product::whereSearch('the_name', $this->query)->get();
        // the_name
        // the_short_description
        // the_long_description
    }

    public function render()
    {
        return view('livewire.web.web-search-section');
    }
}
