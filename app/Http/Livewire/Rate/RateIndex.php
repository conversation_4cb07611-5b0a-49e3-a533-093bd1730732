<?php

namespace App\Http\Livewire\Rate;

use App\Models\Rate;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Livewire\Component;
use Livewire\WithPagination;

class RateIndex extends Component
{
    use WithPagination;

    protected $paginationTheme = 'bootstrap';

    public $perPage = 25;
    public $search = '';
    public $orderBy = 'id';
    public $orderWay = 'desc';
    public $showColumn;

    public $all;
    public $fromDate = null;
    public $toDate = null;
    public $byDate = 'created_at';

    public $selectedRates = [];

    public Rate $rate;
    private $rates;
    public $user;


    public $products = [];

    public $users = [];



    public $filter_products_id = [];

    public $filter_users_id = [];


    public $url;
    public $admin_view_status = '';
    public function mount($admin_view_status = '')
    {
        $this->url = Route::current()->getName();
        $this->admin_view_status = $admin_view_status;
        $this->user = Auth::user();

        $this->rate = new Rate();

        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->toDate = date('Y-m-d');



        $this->products = \App\Models\Product::where('show', 1)->orderBy('sort')->get();

        $this->users = \App\Models\User::orderBy('id', 'desc')->get();


        $this->showColumn = collect([
            'id' => false,
            'slug' => false,


            'product_id' => true,
            'user_id' => true,
            'value' => true,
            'text' => true,

            // 'status' => false,
            'date' => true,
            'time' => false,
        ]);
    }

    public $slug;
    public $rate_id, $product_id, $user_id, $value, $text;
    public $updateMode = false;

    private function resetInputFields()
    {
        $this->slug = '';

        $this->product_id = null;
        $this->user_id = null;
        $this->value = '';
        $this->text = '';
    }


    public function rules()
    {
        return [
            // 'patient_id' => "required|unique:patient_studies,patient_id,$this->patientstudy_id,id,study_id,$this->study_id,deleted_at,NULL",
            // 'slug' => $this-slug,


            'product_id' => 'required',
            'user_id' => 'required',
            'value' => 'required',
            'text' => 'required',
        ];
    }

    public function updated($propertyName)
    {
        $this->validateOnly($propertyName);
    }

    public function store()
    {
        $validatedData = $this->validate();

        Rate::create([
            'slug' => $this->slug,

            'product_id' => $this->product_id,
            'user_id' => $this->user_id,
            'value' => $this->value,
            'text' => $this->text,
        ]);

        session()->flash('message', 'Rate Created Successfully.');

        $this->resetInputFields();

        // $this->emit('close-model'); // Close model to using to jquery
    }

    public function edit($id)
    {
        $this->updateMode = true;
        $rate = Rate::where('id', $id)->first();
        $this->rate_id = $id;
        $this->slug = $rate->slug;


        $this->product_id = $rate->product_id;
        $this->user_id = $rate->user_id;
        $this->value = $rate->value;
        $this->text = $rate->text;
    }

    public function cancel()
    {
        $this->updateMode = false;
        $this->resetInputFields();
    }

    public function update()
    {

        if ($this->rate_id) {
            $rate = Rate::find($this->rate_id);
            $rate->update([
                'slug' => $this->slug,

                'product_id' => $this->product_id,
                'user_id' => $this->user_id,
                'value' => $this->value,
                'text' => $this->text,
            ]);

            $this->updateMode = false;
            session()->flash('message', 'Rate Updated Successfully.');
            $this->resetInputFields();
        }

        $this->emit('close-model'); // Close model to using to jquery
    }

    public function delete($id)
    {
        if ($id) {
            $rate = Rate::find($id);

            $rate->delete();

            session()->flash('message', 'Rate Deleted Successfully.');
        }
    }

    public function restore($id)
    {
        if ($id) {
            $rate = Rate::withTrashed()->find($id);

            $rate->restore();

            session()->flash('message', 'Rate Recovered Successfully.');
        }
    }

    public function updatedFromDate($fromDate)
    {
        $this->all = false;
    }

    public function updatedToDate($toDate)
    {
        $this->all = false;
    }

    public function clearFilter()
    {
        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->byDate = 'created_at';
        $this->fromDate = '';
        $this->toDate = date('Y-m-d');


        $this->filter_products_id = [];

        $this->filter_users_id = [];
    }


    public $select_product;
    public function updatedSelectProduct($val)
    {
        $this->filter_products_id[] = $val;
    }


    public $select_user;
    public function updatedSelectUser($val)
    {
        $this->filter_users_id[] = $val;
    }




    public function gotoPage($page)
    {
        $this->setPage($page);
        $this->emit('gotoTop');
    }

    public function nextPage()
    {
        $this->setPage($this->page + 1);
        $this->emit('gotoTop');
    }

    public function previousPage()
    {
        $this->setPage(max($this->page - 1, 1));
        $this->emit('gotoTop');
    }

    public function render()
    {
        $rates = Rate::livewireSearch($this->search);

        if ($this->all == false)
            $rates = $rates->whereBetween($this->byDate, [$this->fromDate . ' 00:00:00', $this->toDate . ' 23:59:59']);



        if ($this->filter_products_id)
            $rates = $rates->whereIn('product_id', $this->filter_products_id);

        if ($this->filter_users_id)
            $rates = $rates->whereIn('user_id', $this->filter_users_id);


        if ($this->admin_view_status == 'deleted')
            $rates = $rates->onlyTrashed();


        $rates = $rates->orderBy($this->orderBy, $this->orderWay)
            ->paginate($this->perPage);

        return view('livewire.rate.rate-index', compact('rates'));
    }
}
