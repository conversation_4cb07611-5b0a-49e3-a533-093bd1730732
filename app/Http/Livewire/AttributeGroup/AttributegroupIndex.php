<?php

namespace App\Http\Livewire\Attributegroup;

use App\Models\AttributeGroup;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Livewire\Component;
use Livewire\WithPagination;

class AttributegroupIndex extends Component
{
    use WithPagination;

    protected $paginationTheme = 'bootstrap';

    public $perPage = 25;
    public $search = '';
    public $orderBy = 'id';
    public $orderWay = 'desc';
    public $showColumn;

    public $all;
    public $fromDate = null;
    public $toDate = null;
    public $byDate = 'created_at';

    public $selectedAttributeGroups = [];

    public AttributeGroup $attributegroup;
    private $attributegroups;
    public $user;





    public $url;
    public $admin_view_status = '';
    public function mount($admin_view_status = '')
    {
        $this->url = Route::current()->getName();
        $this->admin_view_status = $admin_view_status;
        $this->user = Auth::user();

        $this->attributegroup = new AttributeGroup();

        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->toDate = date('Y-m-d');




        $this->showColumn = collect([
            'id' => false,
            'slug' => false,


            'the_name' => true,
            'type' => true,
            'key' => true,

            // 'status' => false,
            'date' => true,
            'time' => false,
        ]);
    }

    public $slug;
    public $attributegroup_id, $the_name, $type, $key;
    public $updateMode = false;

    private function resetInputFields()
    {
        $this->slug = '';

        $this->the_name = '';
        $this->type = '';
        $this->key = '';
    }


    public function rules()
    {
        return [
            // 'patient_id' => "required|unique:patient_studies,patient_id,$this->patientstudy_id,id,study_id,$this->study_id,deleted_at,NULL",
            // 'slug' => $this-slug,


            'the_name' => 'required',
            'type' => 'required',
            'key' => 'required',
        ];
    }

    public function updated($propertyName)
    {
        $this->validateOnly($propertyName);
    }

    public function store()
    {
        $validatedData = $this->validate();

        AttributeGroup::create([
            'slug' => $this->slug,

            'the_name' => $this->the_name,
            'type' => $this->type,
            'key' => $this->key,
        ]);

        session()->flash('message', 'AttributeGroup Created Successfully.');

        $this->resetInputFields();

        // $this->emit('close-model'); // Close model to using to jquery
    }

    public function edit($id)
    {
        $this->updateMode = true;
        $attributegroup = AttributeGroup::where('id', $id)->first();
        $this->attributegroup_id = $id;
        $this->slug = $attributegroup->slug;


        $this->the_name = $attributegroup->the_name;
        $this->type = $attributegroup->type;
        $this->key = $attributegroup->key;
    }

    public function cancel()
    {
        $this->updateMode = false;
        $this->resetInputFields();
    }

    public function update()
    {

        if ($this->attributegroup_id) {
            $attributegroup = AttributeGroup::find($this->attributegroup_id);
            $attributegroup->update([
                'slug' => $this->slug,

                'the_name' => $this->the_name,
                'type' => $this->type,
                'key' => $this->key,
            ]);

            $this->updateMode = false;
            session()->flash('message', 'AttributeGroup Updated Successfully.');
            $this->resetInputFields();
        }

        $this->emit('close-model'); // Close model to using to jquery
    }

    public function delete($id)
    {
        if ($id) {
            $attributegroup = AttributeGroup::find($id);

            $attributegroup->delete();

            session()->flash('message', 'AttributeGroup Deleted Successfully.');
        }
    }

    public function restore($id)
    {
        if ($id) {
            $attributegroup = AttributeGroup::withTrashed()->find($id);

            $attributegroup->restore();

            session()->flash('message', 'AttributeGroup Recovered Successfully.');
        }
    }

    public function updatedFromDate($fromDate)
    {
        $this->all = false;
    }

    public function updatedToDate($toDate)
    {
        $this->all = false;
    }

    public function clearFilter()
    {
        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->byDate = 'created_at';
        $this->fromDate = '';
        $this->toDate = date('Y-m-d');
    }




    public function gotoPage($page)
    {
        $this->setPage($page);
        $this->emit('gotoTop');
    }

    public function nextPage()
    {
        $this->setPage($this->page + 1);
        $this->emit('gotoTop');
    }

    public function previousPage()
    {
        $this->setPage(max($this->page - 1, 1));
        $this->emit('gotoTop');
    }

    public function render()
    {
        $attributegroups = AttributeGroup::livewireSearch($this->search);

        if ($this->all == false)
            $attributegroups = $attributegroups->whereBetween($this->byDate, [$this->fromDate . ' 00:00:00', $this->toDate . ' 23:59:59']);




        if ($this->admin_view_status == 'deleted')
            $attributegroups = $attributegroups->onlyTrashed();


        $attributegroups = $attributegroups->orderBy($this->orderBy, $this->orderWay)
            ->paginate($this->perPage);

        return view('livewire.attributegroup.attributegroup-index', compact('attributegroups'));
    }
}
