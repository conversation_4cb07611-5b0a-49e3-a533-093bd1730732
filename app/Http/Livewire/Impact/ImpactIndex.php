<?php

namespace App\Http\Livewire\Impact;

use App\Http\Controllers\HomeController;
use App\Models\Impact;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Livewire\Component;
use Livewire\WithFileUploads;
use Livewire\WithPagination;

class ImpactIndex extends Component
{
    use WithPagination;
    use WithFileUploads;

    protected $paginationTheme = 'bootstrap';

    public $perPage = 25;
    public $search = '';
    public $orderBy = 'id';
    public $orderWay = 'desc';
    public $showColumn;

    public $all;
    public $fromDate = null;
    public $toDate = null;
    public $byDate = 'created_at';

    public $selectedImpacts = [];

    public Impact $impact;
    private $impacts;
    public $user;





    public $url;
    public $admin_view_status = '';
    public function mount($admin_view_status = '')
    {
        $this->url = Route::current()->getName();
        $this->admin_view_status = $admin_view_status;
        $this->user = Auth::user();

        $this->impact = new Impact();

        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->toDate = date('Y-m-d');




        $this->showColumn = collect([
            'id' => false,
            'slug' => false,


            'the_title' => true,
            'the_desc' => false,
            'the_sub_desc' => false,
            'link' => true,
            'image' => true,
            'layout' => false,

            // 'status' => false,
            'date' => true,
            'time' => false,
        ]);
    }

    public $slug;
    public $impact_id, $the_title, $the_desc, $the_sub_desc, $link, $image, $layout;
    public $updateMode = false;

    private function resetInputFields()
    {
        $this->slug = '';

        $this->the_title = '';
        $this->the_desc = '';
        $this->the_sub_desc = '';
        $this->link = '';
        $this->image = '';
        $this->layout = '';
    }


    public function rules()
    {
        return [
            // 'patient_id' => "required|unique:patient_studies,patient_id,$this->patientstudy_id,id,study_id,$this->study_id,deleted_at,NULL",
            // 'slug' => $this-slug,


            'the_title' => 'required',
            'the_desc' => 'required',
            'the_sub_desc' => 'required',
            'link' => 'required',
            'image' => 'required',
            // 'layout' => 'required',
        ];
    }

    public function updated($propertyName)
    {
        $this->validateOnly($propertyName);
    }

    public function store()
    {
        $validatedData = $this->validate();

        Impact::create([
            'slug' => $this->slug,

            'the_title' => $this->the_title,
            'the_desc' => $this->the_desc,
            'the_sub_desc' => $this->the_sub_desc,
            'link' => HomeController::saveImageWeb($this->link, 'impact'),
            'image' => HomeController::saveImageWeb($this->image, 'impact'),
            'layout' => $this->layout,
        ]);

        session()->flash('message', 'Impact Created Successfully.');

        $this->resetInputFields();

        // $this->emit('close-model'); // Close model to using to jquery
    }

    public function edit($id)
    {
        $this->updateMode = true;
        $impact = Impact::where('id', $id)->first();
        $this->impact_id = $id;
        $this->slug = $impact->slug;


        $this->the_title = $impact->the_title;
        $this->the_desc = $impact->the_desc;
        $this->the_sub_desc = $impact->the_sub_desc;
        // $this->link = $impact->link;
        // $this->image = $impact->image;
        $this->layout = $impact->layout;
    }

    public function cancel()
    {
        $this->updateMode = false;
        $this->resetInputFields();
    }

    public function update()
    {

        if ($this->impact_id) {
            $impact = Impact::find($this->impact_id);
            $impact->update([
                'slug' => $this->slug,

                'the_title' => $this->the_title,
                'the_desc' => $this->the_desc,
                'the_sub_desc' => $this->the_sub_desc,
                'link' => $this->link ? HomeController::saveImageWeb($this->link, 'impact') : $impact->link,
                'image' => $this->image ? HomeController::saveImageWeb($this->image, 'impact') : $impact->image,
                'layout' => $this->layout,
            ]);

            $this->updateMode = false;
            session()->flash('message', 'Impact Updated Successfully.');
            $this->resetInputFields();
        }

        $this->emit('close-model'); // Close model to using to jquery
    }

    public function delete($id)
    {
        if ($id) {
            $impact = Impact::find($id);

            $impact->delete();

            session()->flash('message', 'Impact Deleted Successfully.');
        }
    }

    public function restore($id)
    {
        if ($id) {
            $impact = Impact::withTrashed()->find($id);

            $impact->restore();

            session()->flash('message', 'Impact Recovered Successfully.');
        }
    }

    public function updatedFromDate($fromDate)
    {
        $this->all = false;
    }

    public function updatedToDate($toDate)
    {
        $this->all = false;
    }

    public function clearFilter()
    {
        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->byDate = 'created_at';
        $this->fromDate = '';
        $this->toDate = date('Y-m-d');
    }




    public function gotoPage($page)
    {
        $this->setPage($page);
        $this->emit('gotoTop');
    }

    public function nextPage()
    {
        $this->setPage($this->page + 1);
        $this->emit('gotoTop');
    }

    public function previousPage()
    {
        $this->setPage(max($this->page - 1, 1));
        $this->emit('gotoTop');
    }

    public function render()
    {
        $impacts = Impact::livewireSearch($this->search);

        if ($this->all == false)
            $impacts = $impacts->whereBetween($this->byDate, [$this->fromDate . ' 00:00:00', $this->toDate . ' 23:59:59']);




        if ($this->admin_view_status == 'deleted')
            $impacts = $impacts->onlyTrashed();


        $impacts = $impacts->orderBy($this->orderBy, $this->orderWay)
            ->paginate($this->perPage);

        return view('livewire.impact.impact-index', compact('impacts'));
    }
}
