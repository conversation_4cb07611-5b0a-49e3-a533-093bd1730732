<?php

namespace App\Http\Livewire\Promocode;

use App\Models\PromoCode;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Livewire\Component;
use Livewire\WithPagination;

class PromocodeIndex extends Component
{
    use WithPagination;

    protected $paginationTheme = 'bootstrap';

    public $perPage = 25;
    public $search = '';
    public $orderBy = 'id';
    public $orderWay = 'desc';
    public $showColumn;

    public $all;
    public $fromDate = null;
    public $toDate = null;
    public $byDate = 'created_at';

    public $selectedPromoCodes = [];

    public PromoCode $promocode;
    private $promocodes;
    public $user;





    public $url;
    public $admin_view_status = '';
    public function mount($admin_view_status = '')
    {
        $this->url = Route::current()->getName();
        $this->admin_view_status = $admin_view_status;
        $this->user = Auth::user();

        $this->promocode = new PromoCode();

        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->toDate = date('Y-m-d');




        $this->showColumn = collect([
            'id' => false,
            'slug' => false,


            'the_name' => true,
            'code' => true,
            'discount_type' => true,
            'discount_amount' => true,
            'type' => false,
            'max_allowed_discount' => true,
            'used_count' => true,
            'active_from_date' => true,
            'active_to_date' => true,
            'status' => true,

            // 'status' => false,
            'date' => false,
            'time' => false,
        ]);
    }

    public $slug;
    public $promocode_id, $the_name, $code, $discount_type, $discount_amount, $type, $max_allowed_discount, $used_count, $active_from_date, $active_to_date, $status;
    public $updateMode = false;

    private function resetInputFields()
    {
        $this->slug = '';

        $this->the_name = '';
        $this->code = '';
        $this->discount_type = '';
        $this->discount_amount = '';
        $this->type = '';
        $this->max_allowed_discount = '';
        $this->used_count = '';
        $this->active_from_date = '';
        $this->active_to_date = '';
        $this->status = '';
    }


    public function rules()
    {
        return [
            // 'patient_id' => "required|unique:patient_studies,patient_id,$this->patientstudy_id,id,study_id,$this->study_id,deleted_at,NULL",
            // 'slug' => $this-slug,


            'the_name' => 'required',
            'code' => 'required',
            'discount_type' => 'required',
            'discount_amount' => 'required',
            'type' => 'required',
            'max_allowed_discount' => 'required',
            'used_count' => 'required',
            'active_from_date' => 'required',
            'active_to_date' => 'required',
            'status' => 'required',
        ];
    }

    public function updated($propertyName)
    {
        $this->validateOnly($propertyName);
    }

    public function store()
    {
        $validatedData = $this->validate();

        PromoCode::create([
            'slug' => $this->slug,

            'the_name' => $this->the_name,
            'code' => $this->code,
            'discount_type' => $this->discount_type,
            'discount_amount' => $this->discount_amount,
            'type' => $this->type,
            'max_allowed_discount' => $this->max_allowed_discount,
            'used_count' => $this->used_count,
            'active_from_date' => $this->active_from_date,
            'active_to_date' => $this->active_to_date,
            'status' => $this->status,
        ]);

        session()->flash('message', 'PromoCode Created Successfully.');

        $this->resetInputFields();

        // $this->emit('close-model'); // Close model to using to jquery
    }

    public function edit($id)
    {
        $this->updateMode = true;
        $promocode = PromoCode::where('id', $id)->first();
        $this->promocode_id = $id;
        $this->slug = $promocode->slug;


        $this->the_name = $promocode->the_name;
        $this->code = $promocode->code;
        $this->discount_type = $promocode->discount_type;
        $this->discount_amount = $promocode->discount_amount;
        $this->type = $promocode->type;
        $this->max_allowed_discount = $promocode->max_allowed_discount;
        $this->used_count = $promocode->used_count;
        $this->active_from_date = $promocode->active_from_date;
        $this->active_to_date = $promocode->active_to_date;
        $this->status = $promocode->status;
    }

    public function cancel()
    {
        $this->updateMode = false;
        $this->resetInputFields();
    }

    public function update()
    {

        if ($this->promocode_id) {
            $promocode = PromoCode::find($this->promocode_id);
            $promocode->update([
                'slug' => $this->slug,

                'the_name' => $this->the_name,
                'code' => $this->code,
                'discount_type' => $this->discount_type,
                'discount_amount' => $this->discount_amount,
                'type' => $this->type,
                'max_allowed_discount' => $this->max_allowed_discount,
                'used_count' => $this->used_count,
                'active_from_date' => $this->active_from_date,
                'active_to_date' => $this->active_to_date,
                'status' => $this->status,
            ]);

            $this->updateMode = false;
            session()->flash('message', 'PromoCode Updated Successfully.');
            $this->resetInputFields();
        }

        $this->emit('close-model'); // Close model to using to jquery
    }

    public function delete($id)
    {
        if ($id) {
            $promocode = PromoCode::find($id);

            $promocode->delete();

            session()->flash('message', 'PromoCode Deleted Successfully.');
        }
    }

    public function restore($id)
    {
        if ($id) {
            $promocode = PromoCode::withTrashed()->find($id);

            $promocode->restore();

            session()->flash('message', 'PromoCode Recovered Successfully.');
        }
    }

    public function updatedFromDate($fromDate)
    {
        $this->all = false;
    }

    public function updatedToDate($toDate)
    {
        $this->all = false;
    }

    public function clearFilter()
    {
        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->byDate = 'created_at';
        $this->fromDate = '';
        $this->toDate = date('Y-m-d');
    }




    public function gotoPage($page)
    {
        $this->setPage($page);
        $this->emit('gotoTop');
    }

    public function nextPage()
    {
        $this->setPage($this->page + 1);
        $this->emit('gotoTop');
    }

    public function previousPage()
    {
        $this->setPage(max($this->page - 1, 1));
        $this->emit('gotoTop');
    }

    public function render()
    {
        $promocodes = PromoCode::livewireSearch($this->search);

        if ($this->all == false)
            $promocodes = $promocodes->whereBetween($this->byDate, [$this->fromDate . ' 00:00:00', $this->toDate . ' 23:59:59']);




        if ($this->admin_view_status == 'deleted')
            $promocodes = $promocodes->onlyTrashed();


        $promocodes = $promocodes->orderBy($this->orderBy, $this->orderWay)
            ->paginate($this->perPage);

        return view('livewire.promocode.promocode-index', compact('promocodes'));
    }
}
