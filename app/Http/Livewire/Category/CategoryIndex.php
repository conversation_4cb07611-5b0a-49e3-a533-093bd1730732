<?php

namespace App\Http\Livewire\Category;

use App\Http\Controllers\HomeController;
use App\Models\Category;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Livewire\Component;
use Livewire\WithFileUploads;
use Livewire\WithPagination;

class CategoryIndex extends Component
{
    use WithPagination;

    use WithFileUploads;

    protected $paginationTheme = 'bootstrap';

    public $perPage = 25;
    public $search = '';
    public $orderBy = 'id';
    public $orderWay = 'desc';
    public $showColumn;

    public $all;
    public $fromDate = null;
    public $toDate = null;
    public $byDate = 'created_at';

    public $selectedCategories = [];

    public Category $category;
    private $categories;
    public $user;


    public $parents = [];



    public $filter_parents_id = [];


    public $url;
    public $admin_view_status = '';
    public function mount($admin_view_status = '')
    {
        $this->url = Route::current()->getName();
        $this->admin_view_status = $admin_view_status;
        $this->user = Auth::user();

        $this->category = new Category();

        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->toDate = date('Y-m-d');



        $this->parents = \App\Models\Category::where('show', 1)->orderBy('sort')->get();


        $this->showColumn = collect([
            'id' => false,
            'slug' => false,


            'the_name' => true,
            'the_description' => false,
            'image' => true,
            'banner' => false,
            'parent_id' => true,

            // 'status' => false,
            'date' => true,
            'time' => false,
        ]);
    }

    public $slug;
    public $category_id, $the_name, $the_description, $image, $banner, $parent_id;
    public $updateMode = false;

    private function resetInputFields()
    {
        $this->slug = '';

        $this->the_name = '';
        $this->the_description = '';
        $this->image = '';
        $this->banner = '';
        $this->parent_id = null;
    }


    public function rules()
    {
        return [
            // 'patient_id' => "required|unique:patient_studies,patient_id,$this->patientstudy_id,id,study_id,$this->study_id,deleted_at,NULL",
            // 'slug' => $this-slug,


            'the_name' => 'required',
            'the_description' => 'required',
            'image' => 'required',
            'banner' => 'required',
            // 'parent_id' => 'required',
        ];
    }

    public function updated($propertyName)
    {
        $this->validateOnly($propertyName);
    }

    public function store()
    {
        $validatedData = $this->validate();

        Category::create([
            'slug' => $this->slug,

            'the_name' => $this->the_name,
            'the_description' => $this->the_description,
            'image' => HomeController::saveImageWeb($this->image, 'category'),
            'banner' => HomeController::saveImageWeb($this->banner, 'category'),
            'parent_id' => $this->parent_id,
        ]);

        session()->flash('message', 'Category Created Successfully.');

        $this->resetInputFields();

        // $this->emit('close-model'); // Close model to using to jquery
    }

    public function edit($id)
    {
        $this->updateMode = true;
        $category = Category::where('id', $id)->first();
        $this->category_id = $id;
        $this->slug = $category->slug;


        $this->the_name = $category->the_name;
        $this->the_description = $category->the_description;
        // $this->image = $category->image;
        // $this->banner = $category->banner;
        $this->parent_id = $category->parent_id;
    }

    public function cancel()
    {
        $this->updateMode = false;
        $this->resetInputFields();
    }

    public function update()
    {

        if ($this->category_id) {
            $category = Category::find($this->category_id);
            $category->update([
                'slug' => $this->slug,

                'the_name' => $this->the_name,
                'the_description' => $this->the_description,
                'image' => $this->image ? HomeController::saveImageWeb($this->image, 'category') : $category->image,
                'banner' => $this->banner ? HomeController::saveImageWeb($this->banner, 'category') : $category->banner,
                'parent_id' => $this->parent_id,
            ]);

            $this->updateMode = false;
            session()->flash('message', 'Category Updated Successfully.');
            $this->resetInputFields();
        }

        $this->emit('close-model'); // Close model to using to jquery
    }

    public function delete($id)
    {
        if ($id) {
            $category = Category::find($id);

            $category->delete();

            session()->flash('message', 'Category Deleted Successfully.');
        }
    }

    public function restore($id)
    {
        if ($id) {
            $category = Category::withTrashed()->find($id);

            $category->restore();

            session()->flash('message', 'Category Recovered Successfully.');
        }
    }

    public function updatedFromDate($fromDate)
    {
        $this->all = false;
    }

    public function updatedToDate($toDate)
    {
        $this->all = false;
    }

    public function clearFilter()
    {
        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->byDate = 'created_at';
        $this->fromDate = '';
        $this->toDate = date('Y-m-d');


        $this->filter_parents_id = [];
    }


    public $select_parent;
    public function updatedSelectParent($val)
    {
        $this->filter_parents_id[] = $val;
    }




    public function gotoPage($page)
    {
        $this->setPage($page);
        $this->emit('gotoTop');
    }

    public function nextPage()
    {
        $this->setPage($this->page + 1);
        $this->emit('gotoTop');
    }

    public function previousPage()
    {
        $this->setPage(max($this->page - 1, 1));
        $this->emit('gotoTop');
    }

    public function render()
    {
        $categories = Category::livewireSearch($this->search);

        if ($this->all == false)
            $categories = $categories->whereBetween($this->byDate, [$this->fromDate . ' 00:00:00', $this->toDate . ' 23:59:59']);



        if ($this->filter_parents_id)
            $categories = $categories->whereIn('parent_id', $this->filter_parents_id);


        if ($this->admin_view_status == 'deleted')
            $categories = $categories->onlyTrashed();


        $categories = $categories->orderBy($this->orderBy, $this->orderWay)
            ->paginate($this->perPage);

        return view('livewire.category.category-index', compact('categories'));
    }
}
