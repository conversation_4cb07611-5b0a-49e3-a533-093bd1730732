<?php

namespace App\Http\Livewire\Attributevalue;

use App\Models\AttributeValue;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Livewire\Component;
use Livewire\WithPagination;

class AttributevalueIndex extends Component
{
    use WithPagination;

    protected $paginationTheme = 'bootstrap';

    public $perPage = 25;
    public $search = '';
    public $orderBy = 'id';
    public $orderWay = 'desc';
    public $showColumn;

    public $all;
    public $fromDate = null;
    public $toDate = null;
    public $byDate = 'created_at';

    public $selectedAttributeValues = [];

    public AttributeValue $attributevalue;
    private $attributevalues;
    public $user;


    public $attribute_groups = [];

    public $products = [];



    public $filter_attribute_groups_id = [];

    public $filter_products_id = [];


    public $url;
    public $admin_view_status = '';
    public function mount($admin_view_status = '')
    {
        $this->url = Route::current()->getName();
        $this->admin_view_status = $admin_view_status;
        $this->user = Auth::user();

        $this->attributevalue = new AttributeValue();

        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->toDate = date('Y-m-d');



        $this->attribute_groups = \App\Models\AttributeGroup::where('show', 1)->orderBy('sort')->get();

        $this->products = \App\Models\Product::where('show', 1)->orderBy('sort')->get();


        $this->showColumn = collect([
            'id' => false,
            'slug' => false,


            'sku' => true,
            'the_name' => true,
            'the_description' => false,
            'price' => true,
            'discount_type' => false,
            'discount_amount' => false,
            'manage_stock' => false,
            'quantity_in_stock' => true,
            'discount_from_date' => false,
            'discount_to_date' => false,
            'status' => true,
            'attribute_group_id' => false,
            'product_id' => true,

            // 'status' => false,
            'date' => true,
            'time' => false,
        ]);
    }

    public $slug;
    public $attributevalue_id, $sku, $the_name, $the_description, $price, $discount_type, $discount_amount, $manage_stock, $quantity_in_stock, $discount_from_date, $discount_to_date, $status, $attribute_group_id, $product_id;
    public $updateMode = false;

    private function resetInputFields()
    {
        $this->slug = '';

        $this->sku = '';
        $this->the_name = '';
        $this->the_description = '';
        $this->price = '';
        $this->discount_type = '';
        $this->discount_amount = '';
        $this->manage_stock = '';
        $this->quantity_in_stock = '';
        $this->discount_from_date = '';
        $this->discount_to_date = '';
        $this->status = '';
        $this->attribute_group_id = null;
        $this->product_id = null;
    }


    public function rules()
    {
        return [
            // 'patient_id' => "required|unique:patient_studies,patient_id,$this->patientstudy_id,id,study_id,$this->study_id,deleted_at,NULL",
            // 'slug' => $this-slug,


            'sku' => 'required',
            'the_name' => 'required',
            'the_description' => 'required',
            'price' => 'required',
            'discount_type' => 'required',
            'discount_amount' => 'required',
            'manage_stock' => 'required',
            'quantity_in_stock' => 'required',
            'discount_from_date' => 'required',
            'discount_to_date' => 'required',
            'status' => 'required',
            'attribute_group_id' => 'required',
            'product_id' => 'required',
        ];
    }

    public function updated($propertyName)
    {
        $this->validateOnly($propertyName);
    }

    public function store()
    {
        $validatedData = $this->validate();

        AttributeValue::create([
            'slug' => $this->slug,

            'sku' => $this->sku,
            'the_name' => $this->the_name,
            'the_description' => $this->the_description,
            'price' => $this->price,
            'discount_type' => $this->discount_type,
            'discount_amount' => $this->discount_amount,
            'manage_stock' => $this->manage_stock,
            'quantity_in_stock' => $this->quantity_in_stock,
            'discount_from_date' => $this->discount_from_date,
            'discount_to_date' => $this->discount_to_date,
            'status' => $this->status,
            'attribute_group_id' => $this->attribute_group_id,
            'product_id' => $this->product_id,
        ]);

        session()->flash('message', 'AttributeValue Created Successfully.');

        $this->resetInputFields();

        // $this->emit('close-model'); // Close model to using to jquery
    }

    public function edit($id)
    {
        $this->updateMode = true;
        $attributevalue = AttributeValue::where('id', $id)->first();
        $this->attributevalue_id = $id;
        $this->slug = $attributevalue->slug;


        $this->sku = $attributevalue->sku;
        $this->the_name = $attributevalue->the_name;
        $this->the_description = $attributevalue->the_description;
        $this->price = $attributevalue->price;
        $this->discount_type = $attributevalue->discount_type;
        $this->discount_amount = $attributevalue->discount_amount;
        $this->manage_stock = $attributevalue->manage_stock;
        $this->quantity_in_stock = $attributevalue->quantity_in_stock;
        $this->discount_from_date = $attributevalue->discount_from_date;
        $this->discount_to_date = $attributevalue->discount_to_date;
        $this->status = $attributevalue->status;
        $this->attribute_group_id = $attributevalue->attribute_group_id;
        $this->product_id = $attributevalue->product_id;
    }

    public function cancel()
    {
        $this->updateMode = false;
        $this->resetInputFields();
    }

    public function update()
    {

        if ($this->attributevalue_id) {
            $attributevalue = AttributeValue::find($this->attributevalue_id);
            $attributevalue->update([
                'slug' => $this->slug,

                'sku' => $this->sku,
                'the_name' => $this->the_name,
                'the_description' => $this->the_description,
                'price' => $this->price,
                'discount_type' => $this->discount_type,
                'discount_amount' => $this->discount_amount,
                'manage_stock' => $this->manage_stock,
                'quantity_in_stock' => $this->quantity_in_stock,
                'discount_from_date' => $this->discount_from_date,
                'discount_to_date' => $this->discount_to_date,
                'status' => $this->status,
                'attribute_group_id' => $this->attribute_group_id,
                'product_id' => $this->product_id,
            ]);

            $this->updateMode = false;
            session()->flash('message', 'AttributeValue Updated Successfully.');
            $this->resetInputFields();
        }

        $this->emit('close-model'); // Close model to using to jquery
    }

    public function delete($id)
    {
        if ($id) {
            $attributevalue = AttributeValue::find($id);

            $attributevalue->delete();

            session()->flash('message', 'AttributeValue Deleted Successfully.');
        }
    }

    public function restore($id)
    {
        if ($id) {
            $attributevalue = AttributeValue::withTrashed()->find($id);

            $attributevalue->restore();

            session()->flash('message', 'AttributeValue Recovered Successfully.');
        }
    }

    public function updatedFromDate($fromDate)
    {
        $this->all = false;
    }

    public function updatedToDate($toDate)
    {
        $this->all = false;
    }

    public function clearFilter()
    {
        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->byDate = 'created_at';
        $this->fromDate = '';
        $this->toDate = date('Y-m-d');


        $this->filter_attribute_groups_id = [];

        $this->filter_products_id = [];
    }


    public $select_attribute_group;
    public function updatedSelectAttributeGroup($val)
    {
        $this->filter_attribute_groups_id[] = $val;
    }


    public $select_product;
    public function updatedSelectProduct($val)
    {
        $this->filter_products_id[] = $val;
    }




    public function gotoPage($page)
    {
        $this->setPage($page);
        $this->emit('gotoTop');
    }

    public function nextPage()
    {
        $this->setPage($this->page + 1);
        $this->emit('gotoTop');
    }

    public function previousPage()
    {
        $this->setPage(max($this->page - 1, 1));
        $this->emit('gotoTop');
    }

    public function render()
    {
        $attributevalues = AttributeValue::livewireSearch($this->search);

        if ($this->all == false)
            $attributevalues = $attributevalues->whereBetween($this->byDate, [$this->fromDate . ' 00:00:00', $this->toDate . ' 23:59:59']);



        if ($this->filter_attribute_groups_id)
            $attributevalues = $attributevalues->whereIn('attribute_group_id', $this->filter_attribute_groups_id);

        if ($this->filter_products_id)
            $attributevalues = $attributevalues->whereIn('product_id', $this->filter_products_id);


        if ($this->admin_view_status == 'deleted')
            $attributevalues = $attributevalues->onlyTrashed();


        $attributevalues = $attributevalues->orderBy($this->orderBy, $this->orderWay)
            ->paginate($this->perPage);

        return view('livewire.attributevalue.attributevalue-index', compact('attributevalues'));
    }
}
