<?php

namespace App\Http\Livewire\Currency;

use App\Models\Currency;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Livewire\Component;
use Livewire\WithPagination;

class CurrencyIndex extends Component
{
    use WithPagination;

    protected $paginationTheme = 'bootstrap';

    public $perPage = 25;
    public $search = '';
    public $orderBy = 'id';
    public $orderWay = 'desc';
    public $showColumn;

    public $all;
    public $fromDate = null;
    public $toDate = null;
    public $byDate = 'created_at';

    public $selectedCurrencies = [];

    public Currency $currency;
    private $currencies;
    public $user;





    public $url;
    public $admin_view_status = '';
    public function mount($admin_view_status = '')
    {
        $this->url = Route::current()->getName();
        $this->admin_view_status = $admin_view_status;
        $this->user = Auth::user();

        $this->currency = new Currency();

        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->toDate = date('Y-m-d');




        $this->showColumn = collect([
            'id' => false,
            'slug' => false,


            'the_name' => true,
            'code' => true,
            'convert_rate' => true,
            'status' => false,

            // 'status' => false,
            'date' => true,
            'time' => false,
        ]);
    }

    public $slug;
    public $currency_id, $the_name, $code, $convert_rate, $status;
    public $updateMode = false;

    private function resetInputFields()
    {
        $this->slug = '';

        $this->the_name = '';
        $this->code = '';
        $this->convert_rate = '';
        $this->status = '';
    }


    public function rules()
    {
        return [
            // 'patient_id' => "required|unique:patient_studies,patient_id,$this->patientstudy_id,id,study_id,$this->study_id,deleted_at,NULL",
            // 'slug' => $this-slug,


            'the_name' => 'required',
            'code' => 'required',
            'convert_rate' => 'required',
            'status' => 'required',
        ];
    }

    public function updated($propertyName)
    {
        $this->validateOnly($propertyName);
    }

    public function store()
    {
        $validatedData = $this->validate();

        Currency::create([
            'slug' => $this->slug,

            'the_name' => $this->the_name,
            'code' => $this->code,
            'convert_rate' => $this->convert_rate,
            'status' => $this->status,
        ]);

        session()->flash('message', 'Currency Created Successfully.');

        $this->resetInputFields();

        // $this->emit('close-model'); // Close model to using to jquery
    }

    public function edit($id)
    {
        $this->updateMode = true;
        $currency = Currency::where('id', $id)->first();
        $this->currency_id = $id;
        $this->slug = $currency->slug;


        $this->the_name = $currency->the_name;
        $this->code = $currency->code;
        $this->convert_rate = $currency->convert_rate;
        $this->status = $currency->status;
    }

    public function cancel()
    {
        $this->updateMode = false;
        $this->resetInputFields();
    }

    public function update()
    {

        if ($this->currency_id) {
            $currency = Currency::find($this->currency_id);
            $currency->update([
                'slug' => $this->slug,

                'the_name' => $this->the_name,
                'code' => $this->code,
                'convert_rate' => $this->convert_rate,
                'status' => $this->status,
            ]);

            $this->updateMode = false;
            session()->flash('message', 'Currency Updated Successfully.');
            $this->resetInputFields();
        }

        $this->emit('close-model'); // Close model to using to jquery
    }

    public function delete($id)
    {
        if ($id) {
            $currency = Currency::find($id);

            $currency->delete();

            session()->flash('message', 'Currency Deleted Successfully.');
        }
    }

    public function restore($id)
    {
        if ($id) {
            $currency = Currency::withTrashed()->find($id);

            $currency->restore();

            session()->flash('message', 'Currency Recovered Successfully.');
        }
    }

    public function updatedFromDate($fromDate)
    {
        $this->all = false;
    }

    public function updatedToDate($toDate)
    {
        $this->all = false;
    }

    public function clearFilter()
    {
        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->byDate = 'created_at';
        $this->fromDate = '';
        $this->toDate = date('Y-m-d');
    }




    public function gotoPage($page)
    {
        $this->setPage($page);
        $this->emit('gotoTop');
    }

    public function nextPage()
    {
        $this->setPage($this->page + 1);
        $this->emit('gotoTop');
    }

    public function previousPage()
    {
        $this->setPage(max($this->page - 1, 1));
        $this->emit('gotoTop');
    }

    public function render()
    {
        $currencies = Currency::livewireSearch($this->search);

        if ($this->all == false)
            $currencies = $currencies->whereBetween($this->byDate, [$this->fromDate . ' 00:00:00', $this->toDate . ' 23:59:59']);




        if ($this->admin_view_status == 'deleted')
            $currencies = $currencies->onlyTrashed();


        $currencies = $currencies->orderBy($this->orderBy, $this->orderWay)
            ->paginate($this->perPage);

        return view('livewire.currency.currency-index', compact('currencies'));
    }
}
