<?php

namespace App\Http\Livewire\Staticpage;

use App\Models\StaticPage;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Livewire\Component;
use Livewire\WithPagination;

class StaticpageIndex extends Component
{
    use WithPagination;

    protected $paginationTheme = 'bootstrap';

    public $perPage = 25;
    public $search = '';
    public $orderBy = 'id';
    public $orderWay = 'desc';
    public $showColumn;

    public $all;
    public $fromDate = null;
    public $toDate = null;
    public $byDate = 'created_at';

    public $selectedStaticPages = [];

    public StaticPage $staticpage;
    private $staticpages;
    public $user;





    public $url;
    public $admin_view_status = '';
    public function mount($admin_view_status = '')
    {
        $this->url = Route::current()->getName();
        $this->admin_view_status = $admin_view_status;
        $this->user = Auth::user();

        $this->staticpage = new StaticPage();

        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->toDate = date('Y-m-d');




        $this->showColumn = collect([
            'id' => false,
            'slug' => false,


            'the_title' => true,
            'page_link' => true,
            'the_content' => false,

            // 'status' => false,
            'date' => true,
            'time' => false,
        ]);
    }

    public $slug;
    public $staticpage_id, $the_title, $page_link, $the_content;
    public $updateMode = false;

    private function resetInputFields()
    {
        $this->slug = '';

        $this->the_title = '';
        $this->page_link = '';
        $this->the_content = '';
    }


    public function rules()
    {
        return [
            // 'patient_id' => "required|unique:patient_studies,patient_id,$this->patientstudy_id,id,study_id,$this->study_id,deleted_at,NULL",
            // 'slug' => $this-slug,


            'the_title' => 'required',
            'page_link' => 'required',
            'the_content' => 'required',
        ];
    }

    public function updated($propertyName)
    {
        $this->validateOnly($propertyName);
    }

    public function store()
    {
        $validatedData = $this->validate();

        StaticPage::create([
            'slug' => $this->slug,

            'the_title' => $this->the_title,
            'page_link' => $this->page_link,
            'the_content' => $this->the_content,
        ]);

        session()->flash('message', 'StaticPage Created Successfully.');

        $this->resetInputFields();

        // $this->emit('close-model'); // Close model to using to jquery
    }

    public function edit($id)
    {
        $this->updateMode = true;
        $staticpage = StaticPage::where('id', $id)->first();
        $this->staticpage_id = $id;
        $this->slug = $staticpage->slug;


        $this->the_title = $staticpage->the_title;
        $this->page_link = $staticpage->page_link;
        $this->the_content = $staticpage->the_content;
    }

    public function cancel()
    {
        $this->updateMode = false;
        $this->resetInputFields();
    }

    public function update()
    {

        if ($this->staticpage_id) {
            $staticpage = StaticPage::find($this->staticpage_id);
            $staticpage->update([
                'slug' => $this->slug,

                'the_title' => $this->the_title,
                'page_link' => $this->page_link,
                'the_content' => $this->the_content,
            ]);

            $this->updateMode = false;
            session()->flash('message', 'StaticPage Updated Successfully.');
            $this->resetInputFields();
        }

        $this->emit('close-model'); // Close model to using to jquery
    }

    public function delete($id)
    {
        if ($id) {
            $staticpage = StaticPage::find($id);

            $staticpage->delete();

            session()->flash('message', 'StaticPage Deleted Successfully.');
        }
    }

    public function restore($id)
    {
        if ($id) {
            $staticpage = StaticPage::withTrashed()->find($id);

            $staticpage->restore();

            session()->flash('message', 'StaticPage Recovered Successfully.');
        }
    }

    public function updatedFromDate($fromDate)
    {
        $this->all = false;
    }

    public function updatedToDate($toDate)
    {
        $this->all = false;
    }

    public function clearFilter()
    {
        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->byDate = 'created_at';
        $this->fromDate = '';
        $this->toDate = date('Y-m-d');
    }




    public function gotoPage($page)
    {
        $this->setPage($page);
        $this->emit('gotoTop');
    }

    public function nextPage()
    {
        $this->setPage($this->page + 1);
        $this->emit('gotoTop');
    }

    public function previousPage()
    {
        $this->setPage(max($this->page - 1, 1));
        $this->emit('gotoTop');
    }

    public function render()
    {
        $staticpages = StaticPage::livewireSearch($this->search);

        if ($this->all == false)
            $staticpages = $staticpages->whereBetween($this->byDate, [$this->fromDate . ' 00:00:00', $this->toDate . ' 23:59:59']);




        if ($this->admin_view_status == 'deleted')
            $staticpages = $staticpages->onlyTrashed();


        $staticpages = $staticpages->orderBy($this->orderBy, $this->orderWay)
            ->paginate($this->perPage);

        return view('livewire.staticpage.staticpage-index', compact('staticpages'));
    }
}
