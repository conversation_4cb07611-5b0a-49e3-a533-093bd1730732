<?php

namespace App\Http\Livewire\Orderstatus;

use App\Models\OrderStatus;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Livewire\Component;
use Livewire\WithPagination;

class OrderstatusIndex extends Component
{
    use WithPagination;

    protected $paginationTheme = 'bootstrap';

    public $perPage = 25;
    public $search = '';
    public $orderBy = 'id';
    public $orderWay = 'desc';
    public $showColumn;

    public $all;
    public $fromDate = null;
    public $toDate = null;
    public $byDate = 'created_at';

    public $selectedOrderStatuses = [];

    public OrderStatus $orderstatus;
    private $orderstatuses;
    public $user;





    public $url;
    public $admin_view_status = '';
    public function mount($admin_view_status = '')
    {
        $this->url = Route::current()->getName();
        $this->admin_view_status = $admin_view_status;
        $this->user = Auth::user();

        $this->orderstatus = new OrderStatus();

        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->toDate = date('Y-m-d');




        $this->showColumn = collect([
            'id' => false,
            'slug' => false,


            'the_name' => true,
            'bg_color' => true,
            'text_color' => true,

            // 'status' => false,
            'date' => true,
            'time' => false,
        ]);
    }

    public $slug;
    public $orderstatus_id, $the_name, $bg_color, $text_color;
    public $updateMode = false;

    private function resetInputFields()
    {
        $this->slug = '';

        $this->the_name = '';
        $this->bg_color = '';
        $this->text_color = '';
    }


    public function rules()
    {
        return [
            // 'patient_id' => "required|unique:patient_studies,patient_id,$this->patientstudy_id,id,study_id,$this->study_id,deleted_at,NULL",
            // 'slug' => $this-slug,


            'the_name' => 'required',
            'bg_color' => 'required',
            'text_color' => 'required',
        ];
    }

    public function updated($propertyName)
    {
        $this->validateOnly($propertyName);
    }

    public function store()
    {
        $validatedData = $this->validate();

        OrderStatus::create([
            'slug' => $this->slug,

            'the_name' => $this->the_name,
            'bg_color' => $this->bg_color,
            'text_color' => $this->text_color,
        ]);

        session()->flash('message', 'OrderStatus Created Successfully.');

        $this->resetInputFields();

        // $this->emit('close-model'); // Close model to using to jquery
    }

    public function edit($id)
    {
        $this->updateMode = true;
        $orderstatus = OrderStatus::where('id', $id)->first();
        $this->orderstatus_id = $id;
        $this->slug = $orderstatus->slug;


        $this->the_name = $orderstatus->the_name;
        $this->bg_color = $orderstatus->bg_color;
        $this->text_color = $orderstatus->text_color;
    }

    public function cancel()
    {
        $this->updateMode = false;
        $this->resetInputFields();
    }

    public function update()
    {

        if ($this->orderstatus_id) {
            $orderstatus = OrderStatus::find($this->orderstatus_id);
            $orderstatus->update([
                'slug' => $this->slug,

                'the_name' => $this->the_name,
                'bg_color' => $this->bg_color,
                'text_color' => $this->text_color,
            ]);

            $this->updateMode = false;
            session()->flash('message', 'OrderStatus Updated Successfully.');
            $this->resetInputFields();
        }

        $this->emit('close-model'); // Close model to using to jquery
    }

    public function delete($id)
    {
        if ($id) {
            $orderstatus = OrderStatus::find($id);

            $orderstatus->delete();

            session()->flash('message', 'OrderStatus Deleted Successfully.');
        }
    }

    public function restore($id)
    {
        if ($id) {
            $orderstatus = OrderStatus::withTrashed()->find($id);

            $orderstatus->restore();

            session()->flash('message', 'OrderStatus Recovered Successfully.');
        }
    }

    public function updatedFromDate($fromDate)
    {
        $this->all = false;
    }

    public function updatedToDate($toDate)
    {
        $this->all = false;
    }

    public function clearFilter()
    {
        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->byDate = 'created_at';
        $this->fromDate = '';
        $this->toDate = date('Y-m-d');
    }




    public function gotoPage($page)
    {
        $this->setPage($page);
        $this->emit('gotoTop');
    }

    public function nextPage()
    {
        $this->setPage($this->page + 1);
        $this->emit('gotoTop');
    }

    public function previousPage()
    {
        $this->setPage(max($this->page - 1, 1));
        $this->emit('gotoTop');
    }

    public function render()
    {
        $orderstatuses = OrderStatus::livewireSearch($this->search);

        if ($this->all == false)
            $orderstatuses = $orderstatuses->whereBetween($this->byDate, [$this->fromDate . ' 00:00:00', $this->toDate . ' 23:59:59']);




        if ($this->admin_view_status == 'deleted')
            $orderstatuses = $orderstatuses->onlyTrashed();


        $orderstatuses = $orderstatuses->orderBy($this->orderBy, $this->orderWay)
            ->paginate($this->perPage);

        return view('livewire.orderstatus.orderstatus-index', compact('orderstatuses'));
    }
}
