<?php

namespace App\Http\Livewire\Address;

use App\Models\Address;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Livewire\Component;
use Livewire\WithPagination;

class AddressIndex extends Component
{
    use WithPagination;

    protected $paginationTheme = 'bootstrap';

    public $perPage = 25;
    public $search = '';
    public $orderBy = 'id';
    public $orderWay = 'desc';
    public $showColumn;

    public $all;
    public $fromDate = null;
    public $toDate = null;
    public $byDate = 'created_at';

    public $selectedAddresses = [];

    public Address $address;
    private $addresses;
    public $user;


    public $users = [];

    public $countries = [];

    public $cities = [];

    public $areas = [];



    public $filter_users_id = [];

    public $filter_countries_id = [];

    public $filter_cities_id = [];

    public $filter_areas_id = [];


    public $url;
    public $admin_view_status = '';
    public function mount($admin_view_status = '')
    {
        $this->url = Route::current()->getName();
        $this->admin_view_status = $admin_view_status;
        $this->user = Auth::user();

        $this->address = new Address();

        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->toDate = date('Y-m-d');



        $this->users = \App\Models\User::orderBy('id', 'desc')->get();

        $this->countries = \App\Models\Country::where('show', 1)->orderBy('sort')->get();

        $this->cities = \App\Models\City::where('show', 1)->orderBy('sort')->get();

        $this->areas = \App\Models\Area::where('show', 1)->orderBy('sort')->get();


        $this->showColumn = collect([
            'id' => false,
            'slug' => false,


            'user_id' => true,
            'nick_name' => true,
            'location' => true,
            'latitude' => false,
            'longitude' => false,
            'country_id' => true,
            'city_id' => true,
            'area_id' => false,
            'note' => true,

            // 'status' => false,
            'date' => true,
            'time' => false,
        ]);
    }

    public $slug;
    public $address_id, $user_id, $nick_name, $location, $latitude, $longitude, $country_id, $city_id, $area_id, $note;
    public $updateMode = false;

    private function resetInputFields()
    {
        $this->slug = '';

        $this->user_id = null;
        $this->nick_name = '';
        $this->location = '';
        $this->latitude = '';
        $this->longitude = '';
        $this->country_id = null;
        $this->city_id = null;
        $this->area_id = null;
        $this->note = '';
    }


    public function rules()
    {
        return [
            // 'patient_id' => "required|unique:patient_studies,patient_id,$this->patientstudy_id,id,study_id,$this->study_id,deleted_at,NULL",
            // 'slug' => $this-slug,


            'user_id' => 'required',
            'nick_name' => 'required',
            'location' => 'required',
            'latitude' => 'required',
            'longitude' => 'required',
            'country_id' => 'required',
            'city_id' => 'required',
            'area_id' => 'required',
            'note' => 'required',
        ];
    }

    public function updated($propertyName)
    {
        $this->validateOnly($propertyName);
    }

    public function store()
    {
        $validatedData = $this->validate();

        Address::create([
            'slug' => $this->slug,

            'user_id' => $this->user_id,
            'nick_name' => $this->nick_name,
            'location' => $this->location,
            'latitude' => $this->latitude,
            'longitude' => $this->longitude,
            'country_id' => $this->country_id,
            'city_id' => $this->city_id,
            'area_id' => $this->area_id,
            'note' => $this->note,
        ]);

        session()->flash('message', 'Address Created Successfully.');

        $this->resetInputFields();

        // $this->emit('close-model'); // Close model to using to jquery
    }

    public function edit($id)
    {
        $this->updateMode = true;
        $address = Address::where('id', $id)->first();
        $this->address_id = $id;
        $this->slug = $address->slug;


        if ($address->user_id)
            $this->user_id = $address->user_id;
        if ($address->nick_name)
            $this->nick_name = $address->nick_name;
        if ($address->location)
            $this->location = $address->location;
        if ($address->latitude)
            $this->latitude = $address->latitude;
        if ($address->longitude)
            $this->longitude = $address->longitude;
        if ($address->country_id)
            $this->country_id = $address->country_id;
        if ($address->city_id)
            $this->city_id = $address->city_id;
        if ($address->area_id)
            $this->area_id = $address->area_id;
        if ($address->note)
            $this->note = $address->note;
    }

    public function cancel()
    {
        $this->updateMode = false;
        $this->resetInputFields();
    }

    public function update()
    {

        if ($this->address_id) {
            $address = Address::find($this->address_id);
            $address->update([
                'slug' => $this->slug,

                'user_id' => $this->user_id,
                'nick_name' => $this->nick_name,
                'location' => $this->location,
                'latitude' => $this->latitude,
                'longitude' => $this->longitude,
                'country_id' => $this->country_id,
                'city_id' => $this->city_id,
                'area_id' => $this->area_id,
                'note' => $this->note,
            ]);

            $this->updateMode = false;
            session()->flash('message', 'Address Updated Successfully.');
            $this->resetInputFields();
        }

        $this->emit('close-model'); // Close model to using to jquery
    }

    public function delete($id)
    {
        if ($id) {
            $address = Address::find($id);

            $address->delete();

            session()->flash('message', 'Address Deleted Successfully.');
        }
    }

    public function restore($id)
    {
        if ($id) {
            $address = Address::withTrashed()->find($id);

            $address->restore();

            session()->flash('message', 'Address Recovered Successfully.');
        }
    }

    public function updatedFromDate($fromDate)
    {
        $this->all = false;
    }

    public function updatedToDate($toDate)
    {
        $this->all = false;
    }

    public function clearFilter()
    {
        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->byDate = 'created_at';
        $this->fromDate = '';
        $this->toDate = date('Y-m-d');


        $this->filter_users_id = [];

        $this->filter_countries_id = [];

        $this->filter_cities_id = [];

        $this->filter_areas_id = [];
    }


    public $select_user;
    public function updatedSelectUser($val)
    {
        $this->filter_users_id[] = $val;
    }


    public $select_country;
    public function updatedSelectCountry($val)
    {
        $this->filter_countries_id[] = $val;
    }


    public $select_city;
    public function updatedSelectCity($val)
    {
        $this->filter_cities_id[] = $val;
    }


    public $select_area;
    public function updatedSelectArea($val)
    {
        $this->filter_areas_id[] = $val;
    }




    public function gotoPage($page)
    {
        $this->setPage($page);
        $this->emit('gotoTop');
    }

    public function nextPage()
    {
        $this->setPage($this->page + 1);
        $this->emit('gotoTop');
    }

    public function previousPage()
    {
        $this->setPage(max($this->page - 1, 1));
        $this->emit('gotoTop');
    }

    public function render()
    {
        $addresses = Address::livewireSearch($this->search);

        if ($this->all == false)
            $addresses = $addresses->whereBetween($this->byDate, [$this->fromDate . ' 00:00:00', $this->toDate . ' 23:59:59']);



        if ($this->filter_users_id)
            $addresses = $addresses->whereIn('user_id', $this->filter_users_id);

        if ($this->filter_countries_id)
            $addresses = $addresses->whereIn('country_id', $this->filter_countries_id);

        if ($this->filter_cities_id)
            $addresses = $addresses->whereIn('city_id', $this->filter_cities_id);

        if ($this->filter_areas_id)
            $addresses = $addresses->whereIn('area_id', $this->filter_areas_id);


        if ($this->admin_view_status == 'deleted')
            $addresses = $addresses->onlyTrashed();


        $addresses = $addresses->orderBy($this->orderBy, $this->orderWay)
            ->paginate($this->perPage);

        return view('livewire.address.address-index', compact('addresses'));
    }
}
