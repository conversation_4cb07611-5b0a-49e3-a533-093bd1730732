<?php

namespace App\Imports\PromoCode;

use App\Models\PromoCode;
use Maatwebsite\Excel\Concerns\ToCollection;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class FullPromoCodesImport implements ToCollection, WithHeadingRow
{
    /**
     * @param Collection $collection
     */
    public function collection(Collection $rows)
    {
        foreach ($rows as $row) {

            $id = trim($row['id']);
            $slug = trim($row['slug']);
            
            
                        $the_name = trim($row['the_name']);

                        $code = trim($row['code']);

                        $discount_type = trim($row['discount_type']);

                        $discount_amount = trim($row['discount_amount']);

                        $type = trim($row['type']);

                        $max_allowed_discount = trim($row['max_allowed_discount']);

                        $used_count = trim($row['used_count']);

                        $active_from_date = trim($row['active_from_date']);

                        $active_to_date = trim($row['active_to_date']);

                        $status = trim($row['status']);


            $promocode = PromoCode::find($id);

            if (!$promocode) {
                $promocode = PromoCode::create([
                    'slug' => $slug,

                    
                        'the_name' => $the_name,
                        'code' => $code,
                        'discount_type' => $discount_type,
                        'discount_amount' => $discount_amount,
                        'type' => $type,
                        'max_allowed_discount' => $max_allowed_discount,
                        'used_count' => $used_count,
                        'active_from_date' => $active_from_date,
                        'active_to_date' => $active_to_date,
                        'status' => $status,
                ]);
            } else {
                $promocode->update([
                    'slug' => $slug,

                    
                        'the_name' => $the_name,
                        'code' => $code,
                        'discount_type' => $discount_type,
                        'discount_amount' => $discount_amount,
                        'type' => $type,
                        'max_allowed_discount' => $max_allowed_discount,
                        'used_count' => $used_count,
                        'active_from_date' => $active_from_date,
                        'active_to_date' => $active_to_date,
                        'status' => $status,
                ]);
            }
        }
    }
}
