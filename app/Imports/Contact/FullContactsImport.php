<?php

namespace App\Imports\Contact;

use App\Models\Contact;
use Maatwebsite\Excel\Concerns\ToCollection;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class FullContactsImport implements ToCollection, WithHeadingRow
{
    /**
     * @param Collection $collection
     */
    public function collection(Collection $rows)
    {
        foreach ($rows as $row) {

            $id = trim($row['id']);
            $slug = trim($row['slug']);
            
            
                        $name = trim($row['name']);

                        $email = trim($row['email']);

                        $phone = trim($row['phone']);

                        $title = trim($row['title']);

                        $message = trim($row['message']);


            $contact = Contact::find($id);

            if (!$contact) {
                $contact = Contact::create([
                    'slug' => $slug,

                    
                        'name' => $name,
                        'email' => $email,
                        'phone' => $phone,
                        'title' => $title,
                        'message' => $message,
                ]);
            } else {
                $contact->update([
                    'slug' => $slug,

                    
                        'name' => $name,
                        'email' => $email,
                        'phone' => $phone,
                        'title' => $title,
                        'message' => $message,
                ]);
            }
        }
    }
}
