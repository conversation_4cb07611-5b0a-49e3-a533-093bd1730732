<?php

namespace App\Imports\Country;

use App\Models\Country;
use Maatwebsite\Excel\Concerns\ToCollection;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class FullCountrysImport implements ToCollection, WithHeadingRow
{
    /**
     * @param Collection $collection
     */
    public function collection(Collection $rows)
    {
        foreach ($rows as $row) {

            $id = trim($row['id']);
            $slug = trim($row['slug']);


            $the_name = trim($row['the_name']);

            $status = trim($row['status']);

            $delivery_fee = trim($row['delivery_fee']);


            $country = Country::find($id);

            if (!$country) {
                $country = Country::create([
                    'slug' => $slug,


                    'the_name' => $the_name,
                    'status' => $status,
                    'delivery_fee' => $delivery_fee,
                ]);
            } else {
                $country->update([
                    'slug' => $slug,


                    'the_name' => $the_name,
                    'status' => $status,
                    'delivery_fee' => $delivery_fee,
                ]);
            }
        }
    }
}
