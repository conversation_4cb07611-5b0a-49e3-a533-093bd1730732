<?php

namespace App\Imports\City;

use App\Models\City;
use Maatwebsite\Excel\Concerns\ToCollection;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class FullCitysImport implements ToCollection, WithHeadingRow
{
    /**
     * @param Collection $collection
     */
    public function collection(Collection $rows)
    {
        foreach ($rows as $row) {

            $id = trim($row['id']);
            $slug = trim($row['slug']);
            
            
                        $the_name = trim($row['the_name']);

                        $delivery_fee = trim($row['delivery_fee']);

                        $status = trim($row['status']);


            $city = City::find($id);

            if (!$city) {
                $city = City::create([
                    'slug' => $slug,

                    
                        'the_name' => $the_name,
                        'delivery_fee' => $delivery_fee,
                        'status' => $status,
                ]);
            } else {
                $city->update([
                    'slug' => $slug,

                    
                        'the_name' => $the_name,
                        'delivery_fee' => $delivery_fee,
                        'status' => $status,
                ]);
            }
        }
    }
}
