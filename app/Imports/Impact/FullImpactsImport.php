<?php

namespace App\Imports\Impact;

use App\Models\Impact;
use Maatwebsite\Excel\Concerns\ToCollection;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class FullImpactsImport implements ToCollection, WithHeadingRow
{
    /**
     * @param Collection $collection
     */
    public function collection(Collection $rows)
    {
        foreach ($rows as $row) {

            $id = trim($row['id']);
            $slug = trim($row['slug']);
            
            
                        $the_title = trim($row['the_title']);

                        $the_desc = trim($row['the_desc']);

                        $the_sub_desc = trim($row['the_sub_desc']);

                        $link = trim($row['link']);

                        $image = trim($row['image']);

                        $layout = trim($row['layout']);


            $impact = Impact::find($id);

            if (!$impact) {
                $impact = Impact::create([
                    'slug' => $slug,

                    
                        'the_title' => $the_title,
                        'the_desc' => $the_desc,
                        'the_sub_desc' => $the_sub_desc,
                        'link' => $link,
                        'image' => $image,
                        'layout' => $layout,
                ]);
            } else {
                $impact->update([
                    'slug' => $slug,

                    
                        'the_title' => $the_title,
                        'the_desc' => $the_desc,
                        'the_sub_desc' => $the_sub_desc,
                        'link' => $link,
                        'image' => $image,
                        'layout' => $layout,
                ]);
            }
        }
    }
}
