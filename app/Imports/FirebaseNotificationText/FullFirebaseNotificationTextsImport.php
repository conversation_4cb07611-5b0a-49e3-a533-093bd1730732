<?php

namespace App\Imports\FirebaseNotificationText;

use App\Models\FirebaseNotificationText;
use Maatwebsite\Excel\Concerns\ToCollection;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class FullFirebaseNotificationTextsImport implements ToCollection, WithHeadingRow
{
    /**
     * @param Collection $collection
     */
    public function collection(Collection $rows)
    {
        foreach ($rows as $row) {

            $id = trim($row['id']);
            $slug = trim($row['slug']);
            
            
                        $the_title = trim($row['the_title']);

                        $the_text = trim($row['the_text']);


            $firebasenotificationtext = FirebaseNotificationText::find($id);

            if (!$firebasenotificationtext) {
                $firebasenotificationtext = FirebaseNotificationText::create([
                    'slug' => $slug,

                    
                        'the_title' => $the_title,
                        'the_text' => $the_text,
                ]);
            } else {
                $firebasenotificationtext->update([
                    'slug' => $slug,

                    
                        'the_title' => $the_title,
                        'the_text' => $the_text,
                ]);
            }
        }
    }
}
