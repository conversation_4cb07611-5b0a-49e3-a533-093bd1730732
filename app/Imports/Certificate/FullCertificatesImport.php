<?php

namespace App\Imports\Certificate;

use App\Models\Certificate;
use Maatwebsite\Excel\Concerns\ToCollection;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class FullCertificatesImport implements ToCollection, WithHeadingRow
{
    /**
     * @param Collection $collection
     */
    public function collection(Collection $rows)
    {
        foreach ($rows as $row) {

            $id = trim($row['id']);
            $slug = trim($row['slug']);
            
            
                        $the_title = trim($row['the_title']);

                        $year = trim($row['year']);


            $certificate = Certificate::find($id);

            if (!$certificate) {
                $certificate = Certificate::create([
                    'slug' => $slug,

                    
                        'the_title' => $the_title,
                        'year' => $year,
                ]);
            } else {
                $certificate->update([
                    'slug' => $slug,

                    
                        'the_title' => $the_title,
                        'year' => $year,
                ]);
            }
        }
    }
}
