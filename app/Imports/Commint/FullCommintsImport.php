<?php

namespace App\Imports\Commint;

use App\Models\Commint;
use Maatwebsite\Excel\Concerns\ToCollection;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class FullCommintsImport implements ToCollection, WithHeadingRow
{
    /**
     * @param Collection $collection
     */
    public function collection(Collection $rows)
    {
        foreach ($rows as $row) {

            $id = trim($row['id']);
            $slug = trim($row['slug']);
            
            
                        $name = trim($row['name']);

                        $email = trim($row['email']);

                        $content = trim($row['content']);

                        $the_model_name = trim($row['the_model_name']);

                        $the_model_id = trim($row['the_model_id']);


            $commint = Commint::find($id);

            if (!$commint) {
                $commint = Commint::create([
                    'slug' => $slug,

                    
                        'name' => $name,
                        'email' => $email,
                        'content' => $content,
                        'the_model_name' => $the_model_name,
                        'the_model_id' => $the_model_id,
                ]);
            } else {
                $commint->update([
                    'slug' => $slug,

                    
                        'name' => $name,
                        'email' => $email,
                        'content' => $content,
                        'the_model_name' => $the_model_name,
                        'the_model_id' => $the_model_id,
                ]);
            }
        }
    }
}
