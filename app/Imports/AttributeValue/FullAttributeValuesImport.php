<?php

namespace App\Imports\AttributeValue;

use App\Models\AttributeValue;
use Maatwebsite\Excel\Concerns\ToCollection;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class FullAttributeValuesImport implements ToCollection, WithHeadingRow
{
    /**
     * @param Collection $collection
     */
    public function collection(Collection $rows)
    {
        foreach ($rows as $row) {

            $id = trim($row['id']);
            $slug = trim($row['slug']);
            
            
                        $sku = trim($row['sku']);

                        $the_name = trim($row['the_name']);

                        $the_description = trim($row['the_description']);

                        $price = trim($row['price']);

                        $discount_type = trim($row['discount_type']);

                        $discount_amount = trim($row['discount_amount']);

                        $manage_stock = trim($row['manage_stock']);

                        $quantity_in_stock = trim($row['quantity_in_stock']);

                        $discount_from_date = trim($row['discount_from_date']);

                        $discount_to_date = trim($row['discount_to_date']);

                        $status = trim($row['status']);


            $attributevalue = AttributeValue::find($id);

            if (!$attributevalue) {
                $attributevalue = AttributeValue::create([
                    'slug' => $slug,

                    
                        'sku' => $sku,
                        'the_name' => $the_name,
                        'the_description' => $the_description,
                        'price' => $price,
                        'discount_type' => $discount_type,
                        'discount_amount' => $discount_amount,
                        'manage_stock' => $manage_stock,
                        'quantity_in_stock' => $quantity_in_stock,
                        'discount_from_date' => $discount_from_date,
                        'discount_to_date' => $discount_to_date,
                        'status' => $status,
                ]);
            } else {
                $attributevalue->update([
                    'slug' => $slug,

                    
                        'sku' => $sku,
                        'the_name' => $the_name,
                        'the_description' => $the_description,
                        'price' => $price,
                        'discount_type' => $discount_type,
                        'discount_amount' => $discount_amount,
                        'manage_stock' => $manage_stock,
                        'quantity_in_stock' => $quantity_in_stock,
                        'discount_from_date' => $discount_from_date,
                        'discount_to_date' => $discount_to_date,
                        'status' => $status,
                ]);
            }
        }
    }
}
