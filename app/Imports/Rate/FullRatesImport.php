<?php

namespace App\Imports\Rate;

use App\Models\Rate;
use Maatwebsite\Excel\Concerns\ToCollection;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class FullRatesImport implements ToCollection, WithHeadingRow
{
    /**
     * @param Collection $collection
     */
    public function collection(Collection $rows)
    {
        foreach ($rows as $row) {

            $id = trim($row['id']);
            $slug = trim($row['slug']);
            
            
                        $value = trim($row['value']);

                        $text = trim($row['text']);


            $rate = Rate::find($id);

            if (!$rate) {
                $rate = Rate::create([
                    'slug' => $slug,

                    
                        'value' => $value,
                        'text' => $text,
                ]);
            } else {
                $rate->update([
                    'slug' => $slug,

                    
                        'value' => $value,
                        'text' => $text,
                ]);
            }
        }
    }
}
