<?php

namespace App\View\Components\Layout\Web;

use App\Models\Category;
use App\Models\WebSitting;
use Illuminate\View\Component;

class header extends Component
{
    /**
     * Create a new component instance.
     *
     * @return void
     */

    public $nav_categories = [];

    public function __construct()
    {
        $this->nav_categories = Category::whereNullOrEmptyOrZero('parent_id')->limit(4)->get();
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\Contracts\View\View|\Closure|string
     */
    public function render()
    {
        // dd($this->commerce);
        return view('components.layout.web.header');
    }
}
