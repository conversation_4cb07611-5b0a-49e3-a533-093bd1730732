<?php

namespace App\Exports\DeviceTokenList;

use App\Models\DeviceTokenList;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

class FullDeviceTokenListsExport implements FromView
{
    private $devicetokenlists = [];

    public $by_date;
    public $from_date;
    public $to_date;

    public function __construct($by_date = null, $from_date = null, $to_date = null)
    {
        $this->by_date = $by_date ?? 'created_at';
        $this->from_date = $from_date ?? date('Y-m-d', strtotime("-150 days"));
        $this->to_date = $to_date ?? date('Y-m-d');

        $this->devicetokenlists = DeviceTokenList::whereBetween($this->by_date, [$this->from_date . ' 00:00:00', $this->to_date . ' 23:59:59']);

        $this->devicetokenlists = $this->devicetokenlists->orderBy('id', 'asc')->get();
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function view(): View
    {
        return view('export.DeviceTokenList.devicetokenlist-export', [
            'devicetokenlists' => $this->devicetokenlists,
            'number' => 0,
        ]);
    }
}
