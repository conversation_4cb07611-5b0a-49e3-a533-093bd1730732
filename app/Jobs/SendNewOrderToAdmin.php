<?php

namespace App\Jobs;

use App\Mail\NewOrderAdmin;
use App\Models\FirebaseNotification;
use App\Models\Order;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;

class SendNewOrderToAdmin implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $order;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(Order $order)
    {
        $this->order = $order;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $users = User::whereRoleIs('owner')->get(); // ->pluck('email')->toArray();

        $emails = $users->pluck('email')->toArray();

        Mail::to($emails)->send(new NewOrderAdmin($this->order));


        foreach ($users as $user) {

            FirebaseNotification::create([
                'user_id' => $user->id,
                // 'notification_text_id' => '',
                // 'image' => '',
                // 'the_page' => '',
                'title' => 'new order',
                'body' => 'there is new order',
                // 'data_id' => '',
                'seen' => false,
                // 'seen_at' => '',
            ]);
        }
    }
}
