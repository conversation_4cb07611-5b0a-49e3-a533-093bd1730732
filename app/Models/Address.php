<?php

namespace App\Models;

use App\Traits\TranslateTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\App;
// use Laratrust\Traits\LaratrustUserTrait;

class Address extends Model
{
    // use LaratrustUserTrait;
    use HasFactory;
    use SoftDeletes;

    // use Translatable trait to translate the columns
    use TranslateTrait;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    // protected $table;

    /**
     * The relations to eager load on every query.
     *
     * @var array
     */
    // protected $with = [];

    /**
     * The relationship counts that should be eager loaded on every query.
     *
     * @var array
     */
    // protected $withCount = [];

    /**
     * The number of models to return for pagination.
     *
     * @var int
     */
    protected $perPage = 15;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'add_by',
        'slug',


        'user_id',
        'nick_name',
        'location',
        'latitude',
        'longitude',
        'country_id',
        'city_id',
        'area_id',
        'note',

        'show',
        'sort',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'show',
        'sort',
    ];


    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'show' => 'boolean',
        'sort' => 'integer',

        // 'the_name' => 'json',
    ];

    public function crud_name()
    {
        return $this->nick_name;
    }

    // public function name($lang = null)
    // {
    //      return $this->translateCol($this->the_name, $lang);
    // }

    public static function livewireSearch($search)
    {
        if (empty($search)) return static::query();

        return static::query()->where(function ($q) use ($search) {
            $q->whereIn('id', array_map('intval', explode(',', $search)));


            $q->orWhere('user_id', 'like', "%$search%");
            $q->orWhereSearch('nick_name', "%$search%");
            $q->orWhereSearch('location', "%$search%");
            $q->orWhereSearch('latitude', "%$search%");
            $q->orWhereSearch('longitude', "%$search%");
            $q->orWhere('country_id', 'like', "%$search%");
            $q->orWhere('city_id', 'like', "%$search%");
            $q->orWhere('area_id', 'like', "%$search%");
            $q->orWhereSearch('note', "%$search%");
        });
    }



    public function user()
    {
        return $this->belongsTo(User::class);
    }


    public function country()
    {
        return $this->belongsTo(Country::class);
    }


    public function city()
    {
        return $this->belongsTo(City::class);
    }


    public function area()
    {
        return $this->belongsTo(Area::class);
    }

    public function price()
    {
        if ($this->area && $this->area->delivery_fee > 0)
            return $this->area->delivery_fee;

        if ($this->city && $this->city->delivery_fee > 0)
            return $this->city->delivery_fee;

        if ($this->country && $this->country->delivery_fee > 0)
            return $this->country->delivery_fee;

        return 0;
    }
}
