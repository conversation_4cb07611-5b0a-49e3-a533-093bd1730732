<?php

namespace App\Models;

use App\Traits\TranslateTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\App;
// use Laratrust\Traits\LaratrustUserTrait;

class Order extends Model
{
    // use LaratrustUserTrait;
    use HasFactory;
    use SoftDeletes;

    // use Translatable trait to translate the columns
    use TranslateTrait;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    // protected $table;

    /**
     * The relations to eager load on every query.
     *
     * @var array
     */
    // protected $with = [];

    /**
     * The relationship counts that should be eager loaded on every query.
     *
     * @var array
     */
    // protected $withCount = [];

    /**
     * The number of models to return for pagination.
     *
     * @var int
     */
    protected $perPage = 15;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'add_by',
        'slug',


        'user_id',
        'order_status_id',
        'total_price',
        'delivery_date',
        'delivery_status',
        'delivery_fee',
        'payment_method',
        'payment_status',
        'address_id',
        'promo_code_id',
        'discount_type',
        'discount_amount',
        'final_price',

        'show',
        'sort',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'show',
        'sort',
    ];


    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'show' => 'boolean',
        'sort' => 'integer',

        // 'the_name' => 'json',
    ];

    public function crud_name()
    {
        return $this->id;
    }

    // public function name($lang = null)
    // {
    //      return $this->translateCol($this->the_name, $lang);
    // }

    public static function livewireSearch($search)
    {
        if (empty($search)) return static::query();

        return static::query()->where(function ($q) use ($search) {
            $q->whereIn('id', array_map('intval', explode(',', $search)));


            $q->orWhere('user_id', 'like', "%$search%");
            $q->orWhere('order_status_id', 'like', "%$search%");
            $q->orWhereSearch('total_price', "%$search%");
            $q->orWhereSearch('delivery_date', "%$search%");
            $q->orWhereSearch('delivery_status', "%$search%");
            $q->orWhereSearch('delivery_fee', "%$search%");
            $q->orWhereSearch('payment_method', "%$search%");
            $q->orWhereSearch('payment_status', "%$search%");
            $q->orWhere('address_id', 'like', "%$search%");
            $q->orWhere('promo_code_id', 'like', "%$search%");
            $q->orWhereSearch('discount_type', "%$search%");
            $q->orWhereSearch('discount_amount', "%$search%");
            $q->orWhereSearch('final_price', "%$search%");
        })->orWhereHas('user', function ($q) use ($search) {
            $q->where('first_name', 'like', "%$search%");
            $q->orWhereSearch('last_name', "%$search%");
            // 
            $q->orWhereSearch('email', "%$search%");
            $q->orWhereSearch('phone', "%$search%");
        })->orWhereHas('address', function ($q) use ($search) {
            $q->whereSearch('location', "%$search%");
            $q->orWhereHas('country', function ($q) use ($search) {
                $q->whereSearch('the_name', "%$search%");
            })->orWhereHas('city', function ($q) use ($search) {
                $q->whereSearch('the_name', "%$search%");
            });
        });
    }



    public function user()
    {
        return $this->belongsTo(User::class)->withTrashed();
    }


    public function order_status()
    {
        return $this->belongsTo(OrderStatus::class)->withTrashed();
    }


    public function address()
    {
        return $this->belongsTo(Address::class)->withTrashed();
    }


    public function promo_code()
    {
        return $this->belongsTo(PromoCode::class)->withTrashed();
    }

    public function items()
    {
        return $this->hasMany(Item::class);
    }

    public function calPrice()
    {
        $subPrice = 0;

        foreach ($this->items as $item) {
            $subPrice += $item->final_price();
        }

        $discountAmount = $this->promo_code ? $this->promo_code->theDiscount($subPrice) : 0;
        $delivery_fee = $this->address ? $this->address->price() : 0;

        $totalPrice = $subPrice + $delivery_fee - $discountAmount;

        $this->update([
            'discount_amount' => $discountAmount,
            'total_price' => $subPrice,
            'delivery_fee' => $delivery_fee,
            'final_price' => $totalPrice,
        ]);
    }
}
