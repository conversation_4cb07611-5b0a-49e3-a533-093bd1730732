<?php

namespace App\Models;

use App\Traits\TranslateTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\App;
// use Laratrust\Traits\LaratrustUserTrait;

class PromoCode extends Model
{
    // use LaratrustUserTrait;
    use HasFactory;
    use SoftDeletes;

    // use Translatable trait to translate the columns
    use TranslateTrait;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    // protected $table;

    /**
     * The relations to eager load on every query.
     *
     * @var array
     */
    // protected $with = [];

    /**
     * The relationship counts that should be eager loaded on every query.
     *
     * @var array
     */
    // protected $withCount = [];

    /**
     * The number of models to return for pagination.
     *
     * @var int
     */
    protected $perPage = 15;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'add_by',
        'slug',


        'the_name',
        'code',
        'discount_type',
        'discount_amount',
        'type',
        'max_allowed_discount',
        'used_count',
        'active_from_date',
        'active_to_date',
        'status',

        'show',
        'sort',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'show',
        'sort',
    ];


    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'show' => 'boolean',
        'sort' => 'integer',

        'the_name' => 'json',
    ];

    public function crud_name()
    {
        return $this->name();
    }

    public function name($lang = null)
    {
        return $this->translateCol($this->the_name, $lang);
    }

    public static function livewireSearch($search)
    {
        if (empty($search)) return static::query();

        return static::query()->where(function ($q) use ($search) {
            $q->whereIn('id', array_map('intval', explode(',', $search)));


            $q->orWhereSearch('the_name', "%$search%");
            $q->orWhereSearch('code', "%$search%");
            $q->orWhereSearch('discount_type', "%$search%");
            $q->orWhereSearch('discount_amount', "%$search%");
            $q->orWhereSearch('type', "%$search%");
            $q->orWhereSearch('max_allowed_discount', "%$search%");
            $q->orWhereSearch('used_count', "%$search%");
            $q->orWhereSearch('active_from_date', "%$search%");
            $q->orWhereSearch('active_to_date', "%$search%");
            $q->orWhereSearch('status', "%$search%");
        });
    }

    public function active()
    {
        return true;

        if ($this->status != 'active') return false;

        if ($this->max_allowed_use_count <= $this->used_count) return false;
        //
        $today = date('y-m-d');

        $is_started = (strtotime($today) >= strtotime($this->active_from_date));
        $is_ended = (strtotime($today) <= strtotime($this->active_untill_date));

        if ($is_started && $is_ended) return true;

        return false;
    }


    public function theDiscount($subPrice)
    {
        $discount = 0.0;
        if (!$this->active()) return $discount;

        if ($this->discount_type == 'fix') {
            $discount = $this->discount_amount;
        } else {
            $discount = ($subPrice * ($this->discount_amount / 100)) - $this->subPrice;
        }

        return $discount;
    }
}
