<?php

namespace App\Models;

use App\Traits\TranslateTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\App;
// use Laratrust\Traits\LaratrustUserTrait;

class Blog extends Model
{
    // use LaratrustUserTrait;
    use HasFactory;
    use SoftDeletes;

    // use Translatable trait to translate the columns
    use TranslateTrait;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    // protected $table;

    /**
     * The relations to eager load on every query.
     *
     * @var array
     */
    // protected $with = [];

    /**
     * The relationship counts that should be eager loaded on every query.
     *
     * @var array
     */
    // protected $withCount = [];

    /**
     * The number of models to return for pagination.
     *
     * @var int
     */
    protected $perPage = 15;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'add_by',
        'slug',


        'the_title',
        'the_content',
        'thumbnail',
        'auther_id',
        'the_summary',

        'show',
        'sort',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'show',
        'sort',
    ];


    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'show' => 'boolean',
        'sort' => 'integer',

        'the_title' => 'json',
        'the_content' => 'json',
        'the_summary' => 'json'
    ];

    public function crud_name()
    {
        return $this->title();
    }

    public function title($lang = null)
    {
        return $this->translateCol($this->the_title, $lang);
    }
    public function content($lang = null)
    {
        return $this->translateCol($this->the_content, $lang);
    }
    public function summary($lang = null)
    {
        return $this->translateCol($this->the_summary, $lang);
    }

    public static function livewireSearch($search)
    {
        if (empty($search)) return static::query();

        return static::query()->where(function ($q) use ($search) {
            $q->whereIn('id', array_map('intval', explode(',', $search)));


            $q->orWhereSearch('the_title', "%$search%");
            $q->orWhereSearch('the_content', "%$search%");
            $q->orWhereSearch('thumbnail', "%$search%");
            $q->orWhere('auther_id', 'like', "%$search%");
            $q->orWhereSearch('the_summary', "%$search%");
        });
    }



    public function auther()
    {
        return $this->belongsTo(User::class, 'auther_id');
    }

    public function commentsBlog()
    {
        return $this->hasMany(Commint::class, 'the_model_id');
    }
}
