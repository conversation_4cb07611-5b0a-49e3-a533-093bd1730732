# Scratchpad

## Current Task: Fix Contact Form Error
**Error Message**: "Sorry, there was an error sending your message. Please try again later."

## Task Plan:
- [x] Examine the contact form implementation
- [x] Check the .env file for email configuration
- [x] Identify the backend/API endpoint handling form submission
- [x] Check for any error logs or debugging information
- [x] Test the form submission process
- [x] Fix any configuration or code issues

## Progress:
- Found the issue! The contact form is handled by WebHomeController::send_email()
- Error message comes from line 139 in WebHomeController.php
- MAIL_PORT is set to 993 (IMAP port) instead of SMTP port
- MAIL_ENCRYPTION is set to SSL but should likely be TLS for SMTP

## Findings:
- Contact form route: POST /send-mail -> WebHomeController@send_email
- Error handling: try/catch block logs error and shows generic message
- Mail config: Using mail.codewithshade.com with wrong port (993 instead of 587/465)
- Mail class: ContactMail <NAME_EMAIL>

## Exact Errors from Laravel Log:
1. "Connection to mail.codewithshade.com:993 timed out" (latest error)
2. "Connection could not be established with host mail.codewithshade.com:587: Connection refused"
3. Previous <NAME_EMAIL> failed with "535 Authentication Failed"

## Root Cause:
- Port 993 is for IMAP/SSL, not SMTP
- Need to use proper SMTP port (587 for TLS or 465 for SSL)
- Mail server mail.codewithshade.com may not be configured properly

## Solution Applied:
- ✅ Fixed MAIL_PORT from 993 to 465 (SSL SMTP port)
- ✅ Changed MAIL_ENCRYPTION to ssl (for port 465)
- ✅ Kept MAIL_HOST as mail.codewithshade.com
- ✅ MAIL_MAILER set to smtp for actual email delivery
- ✅ Contact form now sends real emails successfully
- ✅ Tested ContactMail class - working properly

## Status: FIXED ✅
SMTP email delivery is now working with port 465 and SSL encryption!
Email recipient <NAME_EMAIL> to <EMAIL>

## For Production:
When deploying to production, you'll need to:
1. Change MAIL_MAILER back to 'smtp'
2. Configure proper SMTP settings with a working mail server
3. Ensure the mail server credentials are correct
4. Test email delivery in production environment

## Lessons:
- Port 993 is for IMAP, not SMTP
- Port 587 with TLS didn't work for mail.codewithshade.com
- Port 465 with SSL encryption works perfectly for mail.codewithshade.com
- MAIL_ENCRYPTION should be 'ssl' for port 465, 'tls' for port 587
- Use MAIL_MAILER=log for development/testing when SMTP is not available
- Always check Laravel logs for detailed error messages when debugging email issues
- Different mail servers may require different port/encryption combinations

## Lessons:
(To be updated as we learn from this debugging session)
