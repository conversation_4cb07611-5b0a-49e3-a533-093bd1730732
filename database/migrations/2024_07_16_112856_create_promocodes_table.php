<?php

use App\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePromoCodesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('promo_codes', function (Blueprint $table) {
            $table->bigIncrements('id');

            $table->foreignIdFor(User::class, 'add_by')->default(0);
            $table->string('slug')->nullable();


            $table->json('the_name')->nullable();

            $table->string('code')->nullable();

            $table->string('discount_type')->nullable();

            $table->double('discount_amount')->nullable()->default(0.0);

            $table->string('type')->nullable();

            $table->integer('max_allowed_discount')->nullable()->default(-1);

            $table->integer('used_count')->nullable()->default(0);

            $table->string('active_from_date')->nullable();

            $table->string('active_to_date')->nullable();

            $table->string('status')->nullable()->default('unpublish');


            $table->boolean('show')->default(true);
            $table->integer('sort')->default(1000);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('promo_codes');
    }
}
