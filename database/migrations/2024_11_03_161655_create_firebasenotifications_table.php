<?php

use App\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateFirebaseNotificationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('firebase_notifications', function (Blueprint $table) {
            $table->bigIncrements('id');

            $table->foreignIdFor(User::class, 'add_by')->default(0);
            $table->string('slug')->nullable();


            $table->foreignIdFor(App\Models\User::class)->nullable()->default(0);

            $table->foreignIdFor(App\Models\FirebaseNotificationText::class, 'notification_text_id')->nullable()->default(0);

            $table->string('image')->nullable();

            $table->string('the_page')->nullable()->default('order');

            $table->string('title')->nullable();

            $table->string('body')->nullable();

            $table->integer('data_id')->nullable()->default(0);

            $table->boolean('seen')->nullable()->default(false);

            $table->string('seen_at')->nullable();


            $table->boolean('show')->default(true);
            $table->integer('sort')->default(1000);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('firebase_notifications');
    }
}
