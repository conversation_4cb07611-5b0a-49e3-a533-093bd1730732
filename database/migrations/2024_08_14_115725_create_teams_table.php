<?php

use App\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTeamsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('teams', function (Blueprint $table) {
            $table->bigIncrements('id');

            $table->foreignIdFor(User::class, 'add_by')->default(0);
            $table->string('slug')->nullable();


            $table->json('the_name')->nullable();

            $table->json('the_sub_title')->nullable();

            $table->json('the_bio')->nullable();

            $table->string('image')->nullable();

            $table->string('phone')->nullable();

            $table->string('email')->nullable();

            $table->string('fb_link')->nullable();

            $table->string('tw_link')->nullable();

            $table->string('in_link')->nullable();

            $table->string('ln_link')->nullable();


            $table->boolean('show')->default(true);
            $table->integer('sort')->default(1000);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('teams');
    }
}
